services:
  screaner:
    container_name: screaner
    build:
      context: .
    volumes:
      - ./backend:/app
      - screaner_sockets:/app/sock
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    restart: always
    depends_on:
      - postgres
      - redis
    environment:
      - DEBUG=0
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt
    stdin_open: true # docker run -i
    tty: true        # docker run -t
    command: bash -c "
      supervisord -c supervisord_prod.conf
      "

  postgres:
    container_name: screaner_postgres
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: cgPQKsE3cSykEKy92p1R
      POSTGRES_PASSWORD: dVHZNWUZLL9jV6pP5WZj
      POSTGRES_DB: screaner
    ports:
      - "5432:5432"
    volumes:
      - screaner_postgres_data:/var/lib/postgresql/data

  redis:
    container_name: screaner_redis
    image: redis
    volumes:
      - screaner_redis_data:/data
    restart: always

volumes:
  screaner_postgres_data:
  screaner_sockets:
  screaner_redis_data:
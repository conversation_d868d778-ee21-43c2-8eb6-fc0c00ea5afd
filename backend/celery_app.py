from celery import Celery
from celery.schedules import crontab
import os
from datetime import timedelta


class Config:
    CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL")
    RESULT_BACKEND = os.environ.get("CELERY_RESULT_BACKEND")
    CELERY_TASK_ROUTES = {
        'tasks.*': {
            'queue': 'default',
        },
    }
    TIMEZONE = "UTC"
    broker_connection_retry_on_startup = True
    imports = ("tasks",)


celery_app = Celery('screaner')
celery_app.conf.enable_utc = True
celery_app.config_from_object(Config)
celery_app.conf.timezone = 'UTC'


# Define queues
celery_app.conf.task_queues = {
    "default": {"exchange": "default", "routing_key": "default"},
    "beat": {"exchange": "beat", "routing_key": "beat"},
}
celery_app.conf.task_default_queue = "default"

# Set beat schedule directly
celery_app.conf.beat_schedule = {
    "update_dex_pairs": {
        "task": "update_dex_pairs",
        "schedule": crontab(minute="*/10"),
        "options": {"queue": "beat"},
    },
    # "sync_pairs": {
    #     "task": "sync_pairs",
    #     "schedule": timedelta(seconds=10),
    #     "options": {"queue": "beat"},
    # },
}

celery_app.autodiscover_tasks()
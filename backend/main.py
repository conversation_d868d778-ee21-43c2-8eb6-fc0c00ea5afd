from entry_point import EntryPoint
from exchanges.gate_io.client import GateIOClient
from exchanges.mexc.models import ContractTickerData
from models.pair import PriceComparison, PositionType
from exchanges.dexscreener.models import PairDetails
import asyncio
import logging
import time
if __name__ == "__main__":
    client = GateIOClient()
    # client.get_contract_quanto_multiplier("DYDX_USDT", client.get_random_user())
    # print(client.get_open_futures_orders(client.get_random_user()))

    ticker = ContractTickerData(
        symbol="NAORIS_USDT",
        lastPrice=0.0523

    )


    price_comparison = PriceComparison(
        pair_details=None,  # Assuming PairDetails is not needed for this test
        contract_ticker=ticker
    )
    open_position_info = client.open_position_by_market(
        price_comparison, 
        position_type=PositionType.LONG
    )
    time.sleep(5)
    close_position = client.close_position_by_market(
        open_position_info
    )
    # Uncomment the following lines to enable logging
    # logging.basicConfig(
    #     level=logging.INFO,
    #     format='%(asctime)s - %(levelname)s: %(message)s',
    #     datefmt='%Y-%m-%d %H:%M:%S'
    # )
    # logging.getLogger("httpx").setLevel(logging.WARNING)

    # async def periodic_sync():
    #     entry_point = EntryPoint()
    #     while True:
    #         try:
    #             await entry_point.sync_pirs()
    #         except Exception as e :
    #             logging.exception(f"Error during sync_pirs: {e}")
    #         await asyncio.sleep(3)

    # asyncio.run(periodic_sync())

from exchanges.mexc.client import Mexc<PERSON>lient
from exchanges.gate_io.client import GateIOClient
from exchanges.dexscreener.dex_pair_utils import DexPairValidator
import time
from exchanges.dexscreener.data_repository import Repository
import asyncio
from exchanges.websocket import Websocket
from utils.format_duration import format_duration
import logging
from exchanges.base import ClientType


class EntryPoint:
    __sockets_ids = set()

    async def sync_pirs(self):
        start = time.time()
        await asyncio.gather(
            self.__sync_mex_pirs(),
            self.__sync_gate_pirs(),
        )
        end = time.time()
        logging.info(f"Sync took: {format_duration(end - start)}")


    async def __sync_mex_pirs(self):
        logging.info("Starting price comparison between MEXC Futures and DEXScreener...")

        mexc_client = MexcClient()
        mex_pairs = mexc_client.get_all_pairs()
        dex_remote_repository = Repository()
        dex_pairs = await dex_remote_repository.get_dex_pairs(ClientType.MEXC)
        valid_pairs = DexPairValidator.filter_matching_pairs(mex_pairs, dex_pairs)
        logging.info(f"MEXC pairs: {len(mex_pairs)} | "
                     f"DEX pairs: {len(dex_pairs)} | "
                     f"Valid pairs: {len(valid_pairs)}")

        if valid_pairs:
            new_pairs = [
                i for i in valid_pairs
                if f"{i.pair_details.identifier}_{mexc_client.name}" not in self.__sockets_ids
            ]

            for pair in new_pairs:
                identifier = f"{pair.pair_details.identifier}_{mexc_client.name}"
                self.__sockets_ids.add(identifier)
                ws = Websocket(pair, mexc_client, self.websocket_close_callback)
                asyncio.create_task(ws.start())
                logging.info(f"Started WebSocket for: {identifier}")

    async def __sync_gate_pirs(self):
        logging.info("Starting price comparison between GATE Futures and DEXScreener...")

        gate_client = GateIOClient()
        gate_pairs = gate_client.get_all_pairs()
        dex_remote_repository = Repository()
        dex_pairs = await dex_remote_repository.get_dex_pairs(ClientType.GATE)
        valid_pairs = DexPairValidator.filter_matching_pairs(gate_pairs, dex_pairs)
        logging.info(f"GATE pairs: {len(gate_pairs)} | "
                     f"DEX pairs: {len(dex_pairs)} | "
                     f"Valid pairs: {len(valid_pairs)}")

        if valid_pairs:
            new_pairs = [
                i for i in valid_pairs
                if f"{i.pair_details.identifier}_{gate_client.name}" not in self.__sockets_ids
            ]

            for pair in new_pairs:
                identifier = f"{pair.pair_details.identifier}_{gate_client.name}"
                self.__sockets_ids.add(identifier)
                ws = Websocket(pair, gate_client, self.websocket_close_callback)
                asyncio.create_task(ws.start())
                logging.info(f"Started WebSocket for: {identifier}")

    async def websocket_close_callback(self, identifier: str):
        print(f"removing identifier: {identifier}")
        self.__sockets_ids.discard(identifier)
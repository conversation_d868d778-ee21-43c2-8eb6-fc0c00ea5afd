import json
from typing import Any
import requests
import time
import hmac
import hashlib
from urllib.parse import urlencode
from config import MEXC_BASE_URL, BASE_TOKENS_TO_IGNORE
from models.open_position_info import OpenPositionInfo
from models.pair import PriceComparison, PositionType
from ..base import ExchangeApiClient, ClientType
from .models import ContractTickerData, MexcBalanceData
import urllib.parse


class MexcClient(ExchangeApiClient):
    """MEXC API client implementation"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'MEXC-Python-Client/1.0'
        })
        self.last_error_message = ""

    @staticmethod
    def _url_encode(value: str) -> str: 
            return urllib.parse.quote(value, safe='').replace('+', '%20')
    
    def _generate_signature(self, req_time: str, access_key: str, secret_key: str, request_params: dict[str, Any]) -> str:
        if request_params:
            sorted_items = sorted(request_params.items())
            encoded_params = '&'.join(f"{k}={self._url_encode(str(v))}" for k, v in sorted_items)
        else:
            encoded_params = ""

        sign_str = f"{access_key}{req_time}{encoded_params}"

        signature = hmac.new(
            secret_key.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    @property
    def name(self) -> str:
        return "MEXC"

    @staticmethod
    def _generate_signature_post(req_time: str, access_key: str, secret_key: str, request_params: dict[str, Any]) -> str:
        sorted_params = {k: request_params[k] for k in sorted(request_params)}
        json_body = json.dumps(sorted_params, separators=(',', ':'), ensure_ascii=False)
        
        sign_str = access_key + req_time + json_body

        signature = hmac.new(
            secret_key.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature
    
    def _make_request(self, method: str, endpoint: str, params: dict[str, Any] = None) -> dict[str, Any] | None:
        """Make HTTP request to MEXC API with authentication"""
        url = f"{MEXC_BASE_URL}{endpoint}"
        headers = {}
        req_time = str(int(time.time() * 1000))
        user = self.get_random_user()
        api_key = user.key
        api_secret = user.secret

        try:
            if method == 'GET':
                sign = self._generate_signature(
                            req_time=req_time,
                            access_key=api_key,
                            secret_key=api_secret,
                            request_params=params
                        )
                headers.update({
                    "ApiKey": api_key,
                    "Request-Time": req_time,
                    "signature": sign
                })
                response = self.session.get(url, headers=headers, params=params, timeout=10)
            elif method == 'POST':
                sorted_params = {k: params[k] for k in sorted(params)}
                json_body = json.dumps(sorted_params, separators=(',', ':'), ensure_ascii=False)

                sign = self._generate_signature_post(
                    req_time=req_time,
                    access_key=api_key,
                    secret_key=api_secret,
                    request_params=params
                )
                headers.update({
                    "ApiKey": api_key,
                    "Request-Time": req_time,
                    "signature": sign
                })
                print(f"[DEBUG] sign_str = {api_key}{req_time}{json_body}")
                print(f"[DEBUG] sent body = {json_body}")
                print(f"[DEBUG] headers = {headers}")
                response = self.session.post(url, headers=headers, data=json_body, timeout=10)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.Timeout:
            self.last_error_message = "Request timed out"
            print(f"⏰ MEXC API request timed out: {url}")
        except requests.exceptions.ConnectionError:
            self.last_error_message = "Connection error"
            print(f"🔌 MEXC API connection error: {url}")
        except requests.exceptions.HTTPError as e:
            self.last_error_message = str(e)
            print(f"❌ MEXC API HTTP error: {e}")
        except requests.exceptions.RequestException as e:
            self.last_error_message = str(e)
            print(f"⚠️ MEXC API request failed: {e}")
        except ValueError as e:
            self.last_error_message = f"Invalid JSON response: {e}"
            print(f"🧨 MEXC API invalid JSON: {e}")

        return None

    def get_price_data(self, symbol: str) -> ContractTickerData | None:
        all_pairs = self.get_all_pairs()
        if not all_pairs:
            print(f"❗ Failed to fetch contract data for symbol: {symbol}")
            return None

        for pair in all_pairs:
            if pair.symbol == symbol:
                return pair

        print(f"⚠️ Symbol {symbol} not found in MEXC pair list")
        return None


    def get_all_pairs(self) -> list[ContractTickerData]:
        """Get all available MEXC Futures contract pairs"""
        endpoint = '/api/v1/contract/ticker'

        response = self._make_request('GET', endpoint)

        if not response:
            print("❗ Failed to get MEXC pair list: No response")
            return []

        if not response.get('success', False):
            print(f"❗ Failed to get MEXC pair list: {response.get('message', 'Unknown error')}")
            return []

        try:
            data = response.get("data", [])
            contracts = [
                pair for i in data
                if (pair := ContractTickerData(**i)).base_token not in BASE_TOKENS_TO_IGNORE
            ]
            return contracts
        except Exception as e:
            print(f"❗ Error parsing MEXC pair list response: {e}")
            return []
        
    def get_futures_usdt_balance(self) -> MexcBalanceData | None:
        endpoint = '/api/v1/private/account/assets'

        response = self._make_request("GET", endpoint)

        if not response:
            print("❗ Failed to get MEXC account balance: No response")
            return None

        try:
            data = response.get("data", [])
            contract = next((MexcBalanceData(**item) for item in data if item.get('currency') == 'USDT'), None)
            return contract
        except Exception as e:
            print(f"❗ Error parsing MEXC account balance response: {e}")
            return None

    @property
    def client_type(self) -> ClientType:
        return ClientType.MEXC

    def open_position_by_market(self, price_comparison: PriceComparison,
                                position_type: PositionType) -> list[OpenPositionInfo] | None:
        pass

    def close_position_by_market(self, positions_info: list[OpenPositionInfo]) -> None:
        pass

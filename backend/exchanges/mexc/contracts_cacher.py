from .client import MexcClient
import asyncio
from dataclasses import dataclass
import requests
from models.dex_pair import DexContract
from utils.services import DexContractService
from utils.database import get_session
from sqlalchemy.ext.asyncio import AsyncSession
from exchanges.base import ClientType
from utils.proxy_rotating_client import Proxy<PERSON><PERSON>ting<PERSON><PERSON>, AsyncQueueProcessor


@dataclass
class TokenPair:
    symbol: str
    base: str
    quote: str

class MexcPairCacher:
    def __init__(self, mexc_client: MexcClient):
        self.mexc_client = mexc_client
        self.processor = None
        self.semaphore = asyncio.Semaphore(10)
        self.dex_contracts = []
        self.proxy_client = ProxyRotatingClient()

    async def cache_pairs(self):
        missing_symbols = await self.get_cache_missing_symbols()
        tokens = [TokenPair(symbol, *symbol.split("_")) for symbol in missing_symbols]
        all_contracts = self.__get_all_contracts()

        if not all_contracts:
            return

        processor = AsyncQueueProcessor(max_concurrent_tasks=10)
        self.processor = processor

        async with self.proxy_client:
            for token in tokens:
                contract_address = self.__find_contract_address(token, all_contracts)
                if not contract_address:
                    continue
                await processor.add_task(lambda t=token, c=contract_address: self.__find_provider(t, c))
            await processor.run()

        if not self.dex_contracts:
            return
        print(f"pairs to cache: {len(self.dex_contracts)}")
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        await DexContractService.bulk_create(session, self.dex_contracts)
        await session_gen.aclose()

    async def get_cache_missing_symbols(self):
        mex_pairs = self.mexc_client.get_all_pairs()
        symbols = [i.symbol for i in mex_pairs if i.symbol.split("_")[1] == "USDT"]
        cached_contracts = await self.__fetch_all_contracts(ClientType.MEXC)
        cached_symbols = [i.symbol for i in cached_contracts if i.quote_token == "USDT"]
        missing_symbols = set(symbols).difference(set(cached_symbols))
        return missing_symbols

    @staticmethod
    async def __fetch_all_contracts(client_type: ClientType):
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        pairs = await DexContractService.fetch_all_contracts(session, client_type)
        await session_gen.aclose()
        return pairs

    @staticmethod
    def __get_all_contracts() -> dict | None:
        url = "https://www.mexc.com/api/platform/spot/market-v2/web/symbolsV2"
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.json().get("data", {}).get("symbols", None)
        except Exception as e:
            print(e)
            return None

    @staticmethod
    def __find_contract_address(token_pair: TokenPair, contracts: dict) -> str | None:
        quote_list = contracts.get(token_pair.quote, [])
        if not quote_list:
            return None
        contract_address = next((item.get("ca") for item in quote_list if item.get("vn") == token_pair.base), None)
        return contract_address

    async def __find_provider(self,
                              token: TokenPair,
                              contract_address: str) -> str | None:
        url = f"https://api.dexscreener.com/latest/dex/search?q={contract_address}"
        async with self.semaphore:
            try:
                response = await self.proxy_client.get(
                    url,
                    timeout=10,
                    processor=self.processor,
                    retry_callback=lambda: self.__find_provider(token, contract_address)
                )

                if response.status_code == 429:
                    return  # Proxy rotation handled by ProxyRotatingClient

                response.raise_for_status()
                if (pairs := response.json().get("pairs")) and (chain_id := pairs[0].get("chainId")):
                    contract = DexContract(chain_id=chain_id, address=contract_address,
                                           symbol=token.symbol, base_token=token.base, quote_token=token.quote,
                                           client=ClientType.MEXC)
                    self.dex_contracts.append(contract)
                    print(f"found chain_id '{chain_id}' with contract address '{contract_address}'")
            except Exception as e:
                print(f"Failed to fetch url {url}: {e}")
                return

ticker_schema = {
    "type": "object",
    "required": [
        "symbol",
        "lastPrice",
        "riseFallRate",
        "fairPrice",
        "indexPrice",
        "volume24",
        "amount24",
        "maxBidPrice",
        "minAskPrice",
        "lower24Price",
        "high24Price",
        "timestamp",
        "bid1",
        "ask1",
        "holdVol",
        "riseFallValue",
        "fundingRate",
        "zone",
        "riseFallRates",
        "riseFallRatesOfTimezone"
    ],
    "properties": {
        "symbol": {"type": "string"},
        "lastPrice": {"type": "number"},
        "riseFallRate": {"type": "number"},
        "fairPrice": {"type": "number"},
        "indexPrice": {"type": "number"},
        "volume24": {"type": "number"},
        "amount24": {"type": "number"},
        "maxBidPrice": {"type": "number"},
        "minAskPrice": {"type": "number"},
        "lower24Price": {"type": "number"},
        "high24Price": {"type": "number"},
        "timestamp": {"type": "integer"},
        "bid1": {"type": "number"},
        "ask1": {"type": "number"},
        "holdVol": {"type": "number"},
        "riseFallValue": {"type": "number"},
        "fundingRate": {"type": "number"},
        "zone": {"type": "string"},
        "riseFallRates": {
        "type": "array",
          "items": {
            "type": ["number", "null"]
          }
        },
        "riseFallRatesOfTimezone": {
            "type": "array",
            "items": {"type": "number"}
        }
    },
    "additionalProperties": False
}
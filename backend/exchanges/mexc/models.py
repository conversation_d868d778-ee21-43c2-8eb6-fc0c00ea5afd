from pydantic import BaseModel, PrivateAttr
from typing import Union, Any


class ContractTickerData(BaseModel):
    symbol: str | None = None
    lastPrice: float | None = None
    bid1: float | None = None
    ask1: float | None = None
    volume24: float | None = None
    amount24: float | None = None
    holdVol: float | None = None
    lower24Price: float | None = None
    high24Price: float | None = None
    riseFallRate: float | None = None
    riseFallValue: float | None = None
    indexPrice: float | None = None
    fairPrice: float | None = None
    fundingRate: float | None = None
    maxBidPrice: float | None = None
    minAskPrice: float | None = None
    timestamp: int | None = None

    _original_json: dict[str, Any] = PrivateAttr()

    def __init__(self):
        super().__init__()
        
    def __init__(self, **data):
        super().__init__(**data)
        self._original_json = data.copy()

    def __str__(self):
        return f"""
        {{
            symbol: {self.symbol},
            lastPrice: {self.lastPrice},
            volume24: {self.volume24},
        }}
        """

    @property
    def base_token(self) -> str:
        base_token, _ = self.symbol.split("_")
        return base_token

    @classmethod
    def from_gate_io(cls, item: dict) -> "ContractTickerData":
        def safe_float(value):
            try:
                return float(value)
            except (TypeError, ValueError):
                return None

        return cls(
            symbol=item.get("contract"),
            lastPrice=safe_float(item.get("last")),
            bid1=safe_float(item.get("highest_bid")),
            ask1=safe_float(item.get("lowest_ask")),
            volume24=safe_float(item.get("volume_24h_base")),
            amount24=safe_float(item.get("volume_24h_quote")),
            holdVol=safe_float(item.get("total_size")),
            lower24Price=safe_float(item.get("low_24h")),
            high24Price=safe_float(item.get("high_24h")),
            riseFallRate=safe_float(item.get("change_percentage")),
            riseFallValue=safe_float(item.get("change_price")),
            indexPrice=safe_float(item.get("index_price")),
            fairPrice=safe_float(item.get("mark_price")),
            fundingRate=safe_float(item.get("funding_rate_indicative")),
            maxBidPrice=safe_float(item.get("highest_bid")),
            minAskPrice=safe_float(item.get("lowest_ask")),
        )


class MexcContractResponse(BaseModel):
    success: bool | None = None
    code: int | None = None
    data: Union[ContractTickerData, list] | None = None


class MexcBalanceData(BaseModel):
    currency: str | None = None
    positionMargin: float | None = None
    availableBalance: float | None = None
    cashBalance: float | None = None
    frozenBalance: float | None = None
    equity: float | None = None
    unrealized: float | None = None
    bonus: float | None = None
    availableCash: float | None = None
    availableOpen: float | None = None

    def __str__(self):
        return f"""
        {{
            currency: {self.currency},
            positionMargin: {self.positionMargin},
            availableBalance: {self.availableBalance},
            equity: {self.equity},
        }}
        """
from .models import ContractTickerData
import asyncio
import websockets
import json
from jsonschema import validate, ValidationError
from .ticker_schema import ticker_schema
import logging
from ..base_websocket import BaseWebSocket


class Websocket(BaseWebSocket):
    async def start(self):
        retries = 0
        max_retries = 10
        data = {
            "method": "sub.ticker",
            "param": {
                "symbol": self.symbol
            }
        }
        url = "wss://contract.mexc.com/edge"
        try:
            async with websockets.connect(url) as websocket:
                self.websocket = websocket
                self.is_connected = True
                asyncio.create_task(
                    self.send_ping(websocket, payload={"method": "ping"}, reconnect_callback=self.reconnect)
                )
                await websocket.send(json.dumps(data))
                while retries < max_retries:
                    try:
                        raw = await websocket.recv()
                        message = json.loads(raw).get("data")
                        validate(instance=message, schema=ticker_schema)
                        pair = ContractTickerData(**message)
                        if self.is_connected:
                            await self.callback_func(pair)
                    except json.JSONDecodeError as e:
                        logging.error(f"[MEXC Websocket]: Reason: {e}")
                    except ValidationError:
                        pass
        except Exception as e:
            code = getattr(e, "code", None)
            if code == 1000:
                logging.info("[MEXC Websocket] Normal closure.")
                return
            logging.error(f"[MEXC Websocket]: Reason: {e}")
            self.is_connected = False
            await asyncio.sleep(3)

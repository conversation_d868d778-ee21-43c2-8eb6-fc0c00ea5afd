"""Base protocol for exchange API clients"""

from typing import <PERSON>V<PERSON>
from abc import ABC, abstractmethod
from pydantic import BaseModel
from models.pair import PriceComparison, PositionType
from models.open_position_info import OpenPositionInfo, ClientUser
from enum import StrEnum
from os import path
import sys
import json
import random

# Generic type for exchange-specific pair data
T = TypeVar('T', bound=BaseModel)


class ClientType(StrEnum):
    MEXC = "MEXC"
    GATE = "GATE"

    @property
    def name(self) -> str:
        return self.value

    @property
    def tg_chat_id(self) -> str:
        match self:
            case ClientType.MEXC:
                return "-1002836712464"
            case ClientType.GATE:
                return "-1002466984358"
            case _:
                raise Exception(f"Unknown client type: {self}")


class ExchangeApiClient(ABC):
    """Abstract base class defining the interface for a trading API client."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get the name of the exchange."""
        pass

    @property
    @abstractmethod
    def client_type(self) -> ClientType:
        """Get the type of the exchange."""
        pass

    @abstractmethod
    def open_position_by_market(self, price_comparison: PriceComparison,
                                position_type: PositionType) -> list[OpenPositionInfo] | None:
        pass

    @abstractmethod
    def close_position_by_market(self, positions_info: list[OpenPositionInfo]):
        pass

    def get_users(self) -> list[ClientUser]:
        __import__(type(self).__module__)
        module = sys.modules[type(self).__module__]
        parent_path = path.dirname(module.__file__)
        json_path = path.join(parent_path, "users.json")
        with open(json_path, "r") as f:
            users_data = json.load(f)
            return [ClientUser(**u) for u in users_data]

    def get_random_user(self) -> ClientUser:
        all_users = self.get_users()
        random_user = random.choice(all_users)
        return random_user

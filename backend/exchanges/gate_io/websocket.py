from ..mexc.models import ContractTickerData
import asyncio
import websockets
import json
from jsonschema import validate, ValidationError
from .ticker_schema import futures_ticker_schema
import logging
from ..base_websocket import BaseWebSocket
import time
import hmac
import hashlib


class Websocket(BaseWebSocket):
    async def start(self):
        retries = 0
        max_retries = 10
        url = "wss://fx-ws.gateio.ws/v4/ws/usdt"
        try:
            async with websockets.connect(url) as websocket:
                self.websocket = websocket
                self.is_connected = True
                asyncio.create_task(
                    self.send_ping(websocket,
                                   payload={"time" : int(time.time() * 1000), "channel" : "futures.ping"},
                                   reconnect_callback=self.reconnect)
                )

                login_payload = self.__build_login_request()
                await websocket.send(json.dumps(login_payload))

                await websocket.send(json.dumps(self.__build_main_request()))

                while retries < max_retries:
                    try:
                        raw = await websocket.recv()
                        message = json.loads(raw)
                        validate(instance=message, schema=futures_ticker_schema)
                        result = message.get("result")
                        pairs = [ContractTickerData.from_gate_io(i) for i in result]
                        if pairs and self.is_connected:
                            await self.callback_func(pairs[0])
                    except json.JSONDecodeError as e:
                        logging.error(f"[MEXC Websocket]: Reason: {e}")
                    except ValidationError:
                        pass
        except Exception as e:
            code = getattr(e, "code", None)
            if code == 1000:
                logging.info("[MEXC Websocket] Normal closure.")
                return
            logging.error(f"[MEXC Websocket]: Reason: {e}")
            self.is_connected = False
            await asyncio.sleep(3)

    def __build_main_request(self):
        return {
            "time": self.__get_ts(),
            "channel": "futures.tickers",
            "event": "subscribe",
            "payload": [self.symbol]
        }

    def __build_login_request(self):
        channel_login = "futures.login"
        ts = self.__get_ts()
        req_id = f"{self.__get_ts_ms()}-1"
        request_param = b""
        sign = self.__get_signature(self.user.secret, channel_login, request_param, ts)

        payload = {
            "api_key": self.user.key,
            "signature": sign,
            "timestamp": str(ts),
            "req_id": req_id
        }

        return {
            "time": ts,
            "channel": channel_login,
            "event": "api",
            "payload": payload
        }

    @staticmethod
    def __get_ts():
        return int(time.time())

    @staticmethod
    def __get_ts_ms():
        return int(time.time() * 1000)

    @staticmethod
    def __get_signature(secret, channel, request_param_bytes, ts):
        key = f"api\n{channel}\n{request_param_bytes.decode()}\n{ts}"
        return hmac.new(secret.encode(), key.encode(), hashlib.sha512).hexdigest()

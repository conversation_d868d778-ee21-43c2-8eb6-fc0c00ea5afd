futures_ticker_schema = {
    "type": "object",
    "required": ["time", "time_ms", "channel", "event", "result"],
    "properties": {
        "time": {"type": "integer"},
        "time_ms": {"type": "integer"},
        "channel": {"type": "string", "const": "futures.tickers"},
        "event": {"type": "string", "const": "update"},
        "result": {
            "type": "array",
            "items": {
                "type": "object",
                "required": [
                    "contract",
                    "last",
                    "change_percentage",
                    "total_size",
                    "volume_24h",
                    "volume_24h_base",
                    "volume_24h_quote",
                    "volume_24h_settle",
                    "mark_price",
                    "funding_rate",
                    "funding_rate_indicative",
                    "index_price",
                    "low_24h",
                    "high_24h",
                    "price_type",
                    "change_from",
                    "change_price"
                ],
                "properties": {
                    "contract": {"type": "string"},
                    "last": {"type": "string"},
                    "change_percentage": {"type": "string"},
                    "total_size": {"type": "string"},
                    "volume_24h": {"type": "string"},
                    "volume_24h_base": {"type": "string"},
                    "volume_24h_quote": {"type": "string"},
                    "volume_24h_settle": {"type": "string"},
                    "mark_price": {"type": "string"},
                    "funding_rate": {"type": "string"},
                    "funding_rate_indicative": {"type": "string"},
                    "index_price": {"type": "string"},
                    "quanto_base_rate": {"type": "string"},
                    "low_24h": {"type": "string"},
                    "high_24h": {"type": "string"},
                    "price_type": {"type": "string"},
                    "change_from": {"type": "string"},
                    "change_price": {"type": "string"}
                },
                "additionalProperties": False
            }
        }
    },
    "additionalProperties": False
}

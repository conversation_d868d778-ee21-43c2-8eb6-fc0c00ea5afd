import requests
import time
import hmac
import hashlib
import json
import urllib.parse
from utils.position_calculator import PositionCalculator
from exchanges.mexc.models import ContractTickerData
from models.open_position_info import OpenPositionInfo
from config import GATEIO_BASE_URL, BASE_TOKENS_TO_IGNORE
from typing import Any
from models.pair import PriceComparison, PositionType
from ..base import ExchangeApiClient, ClientType, ClientUser


class GateIOClient(ExchangeApiClient):
    """MEXC API client implementation"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json', 
            'Content-Type': 'application/json'
        })
        self.last_error_message = ""

    @staticmethod
    def _url_encode(value: str) -> str:
        return urllib.parse.quote(value, safe='').replace('+', '%20')
    
    @staticmethod
    def _generate_signature(method: str, url_path: str, api_key: str, api_secret: str,
                query_string: str = "", payload_string: str = "") -> dict[str, str]:
        timestamp = str(time.time())
        
        hashed_payload = hashlib.sha512(payload_string.encode('utf-8')).hexdigest()
        
        sign_string = f"{method.upper()}\n{url_path}\n{query_string}\n{hashed_payload}\n{timestamp}"
        
        signature = hmac.new(
            api_secret.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha512
        ).hexdigest()

        return {
            "KEY": api_key,
            "Timestamp": timestamp,
            "SIGN": signature
        }
    
    def _make_request(self, method: str,
                      endpoint: str,
                      api_key: str,
                      api_secret: str,
                      params: dict[str, Any] = None) -> dict[str, Any] | None:
        base_url = GATEIO_BASE_URL
        full_url = f"{base_url}{endpoint}"

        method = method.upper()
        query_string = ""
        payload_string = ""

        if method == "GET" and params:
            query_string = self._url_encode(sorted(params.items()))
            full_url += f"?{query_string}"
        elif method in ("POST", "PUT") and params:
            payload_string = json.dumps(params, separators=(',', ':'), ensure_ascii=False)

        headers = self._generate_signature(
            method=method,
            url_path=endpoint,
            api_key=api_key,
            api_secret=api_secret,
            query_string=query_string,
            payload_string=payload_string
        )

        if method in ("POST", "PUT"):
            headers["Content-Type"] = "application/json"

        try:
            if method == "GET":
                response = self.session.get(full_url, headers=headers, timeout=10)
            else:
                response = self.session.request(method, full_url, headers=headers, data=payload_string, timeout=10)

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"❌ Gate.io API error: {e}")
            if e.response is not None:
                print(f"🔎 Response: {e.response.text}")
            return None


    def get_all_pairs(self) -> list[ContractTickerData]:
        endpoint = '/api/v4/futures/usdt/tickers'
        random_user = self.get_random_user()
        response = self._make_request('GET', endpoint, api_key=random_user.key, api_secret=random_user.secret)

        if not response:
            print("❗ Failed to get Gate.io pair list: No response")
            return []

        tickers: list[ContractTickerData] = []

        try:
            for item in response:
                ticker = ContractTickerData.from_gate_io(item)
                if ticker.base_token in BASE_TOKENS_TO_IGNORE:
                    continue
                tickers.append(ticker)

        except Exception as e:
            print(f"❗ Error parsing Gate.io pair list: {e}")
            return []

        return tickers
    
    def get_contract_quanto_multiplier(self, symbol: str, user: ClientUser) -> tuple[float, int, float, float] | None:
        endpoint = f"/api/v4/futures/usdt/contracts/{symbol}"
        data = self._make_request("GET", endpoint, api_key=user.key, api_secret=user.secret)

        if not data:
            return None

        print(f"data: {data}")
        leverage_max = int(data['leverage_max'])
        quanto_multiplier = float(data['quanto_multiplier'])
        contact_min_qty = float(data['order_size_min'])
        contract_max_qty = float(data['order_size_max'])
        maintenance_rate = float(data['maintenance_rate'])
        return quanto_multiplier, leverage_max, contact_min_qty, contract_max_qty, maintenance_rate
        
    def get_open_futures_orders(self, user: ClientUser) -> list[dict] | None:
        endpoint = '/api/v4/futures/usdt/positions/DYDX_USDT'
        response = self._make_request("GET", endpoint, api_key=user.key, api_secret=user.secret)
        print(f"Open orders response: {response}")
        return response if response else None

    def is_open_futures_order_open(self, price_comparison: PriceComparison, user: ClientUser) -> bool:
        endpoint = '/api/v4/futures/usdt/open_orders/{price_comparison.contract_ticker.symbol}'

        response = self._make_request("GET", endpoint, api_key=user.key, api_secret=user.secret)
        return response if response else None

    def get_futures_usdt_balance(self, user: ClientUser) -> float | None:
        endpoint = '/api/v4/futures/usdt/accounts'

        response = self._make_request("GET", endpoint, api_key=user.key, api_secret=user.secret)

        if not response:
            print("❗ Failed to get MEXC account balance: No response")
            return None

        try:
            data = response.get("total", None)
            
            return float(data)
        except Exception as e:
            print(f"❗ Error parsing MEXC account balance response: {e}")
            return None

    def all_currencies(self):
        endpoint = "/api/v4/spot/currencies"
        random_user = self.get_random_user()
        data = self._make_request("GET", endpoint, api_key=random_user.key, api_secret=random_user.secret)
        return data

    def open_position_by_market(self, price_comparison: PriceComparison,
                                position_type: PositionType) -> list[OpenPositionInfo] | None:
        positions = [self.__open_position(u, price_comparison, position_type) for u in self.get_users()]
        return [i for i in positions if i]

    def __open_position(self, user: ClientUser,
                        price_comparison: PriceComparison,
                        position_type: PositionType) -> OpenPositionInfo | None:
        endpoint = '/api/v4/futures/usdt/orders'
        balance = self.get_futures_usdt_balance(user)

        quanto_multiplier, leverage_max, contact_min_qty, contract_max_qty, maintenance_rate = self.get_contract_quanto_multiplier(price_comparison.contract_ticker.symbol, user)
        position_calculator = PositionCalculator(price_comparison,
                                                  balance, 
                                                  quanto_multiplier, 
                                                  leverage_max,
                                                  contact_min_qty,
                                                  contract_max_qty,
                                                  maintenance_rate)

        size = position_calculator.calculate_position_size(is_long = True if position_type == PositionType.LONG else False)
        stop_loss_price = position_calculator.calculate_stoploss()
        print(f"Calculated position size: {size}, Stop loss price: {stop_loss_price}")
        if position_type == PositionType.SHORT:
            size = -size
            print(f"Calculated position size: {size}, Stop loss price: {stop_loss_price}")

        position_info = OpenPositionInfo(user=user,
                                         position_size=size,
                                         leverage=leverage_max,
                                         stop_loss=stop_loss_price,
                                         contract=price_comparison.contract_ticker.symbol,
                                         direction=position_type)
        params = {
            "contract": price_comparison.contract_ticker.symbol,
            "size": size,
            "price": 0,
            "tif": "ioc",
            "reduce_only": False,
            "close": False,
            "side": position_type.direction,
            "stop_loss_price": stop_loss_price,
            "text": f"t-{price_comparison.contract_ticker.symbol}-entry"
        }

        print(f"Opening position with params: {params}")
        try:
            info = self._make_request("POST", endpoint, params=params, api_key=user.key, api_secret=user.secret)
            print(f"Gate.com position open response: {info}")
            return position_info
        except Exception as e:
            print(f"❗ Error Gate.com position open response: {e}")
            return None
         
    def close_position_by_market(self, positions_info: list[OpenPositionInfo]):
        for position in positions_info:
            self.__close_position(position)

    def __close_position(self, position_info: OpenPositionInfo):
        endpoint = '/api/v4/futures/usdt/orders'
        if position_info.direction.LONG:
            direction = "sell"
            auto_size = "close_long"
        else:
            direction = "buy"
            auto_size = "close_short"

        params = {
            "contract": position_info.contract,
            "size": 0,
            "price": 0,
            "close": False,
            "tif": "ioc",
            "reduce_only": True,
            "auto_size": auto_size,
            "side": direction,
            "text": f"t-{position_info.contract}-entry-close"
        }

        try:
            self._make_request("POST",
                               endpoint,
                               params=params,
                               api_key=position_info.user.key,
                               api_secret=position_info.user.secret)
        except Exception as e:
            print(f"❗ Error Gate.com position close response: {e}")

    @property
    def name(self) -> str:
        return "GATE.IO"

    @property
    def client_type(self) -> ClientType:
        return ClientType.GATE

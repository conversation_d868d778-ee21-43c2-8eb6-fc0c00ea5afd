from .client import GateIOClient
import asyncio
from dataclasses import dataclass
from models.dex_pair import DexContract
from exchanges.dexscreener.models import PairDetails
from utils.services import DexContractService
from utils.database import get_session
from sqlalchemy.ext.asyncio import AsyncSession
from exchanges.base import ClientType
from utils.proxy_rotating_client import ProxyRotatingClient, AsyncQueueProcessor


@dataclass
class TokenPair:
    symbol: str
    base: str
    quote: str

class GatePairCacher:
    def __init__(self, gate_client: GateIOClient):
        self.gate_client = gate_client
        self.processor = None
        self.semaphore = asyncio.Semaphore(10)
        self.dex_contracts = []
        self.proxy_client = ProxyRotatingClient()

    async def cache_pairs(self):
        missing_symbols = await self.get_cache_missing_symbols()
        tokens = [TokenPair(symbol, *symbol.split("_")) for symbol in missing_symbols]
        all_contracts = self.__get_all_contracts()

        if not all_contracts:
            return

        processor = AsyncQueueProcessor(max_concurrent_tasks=10)
        self.processor = processor

        async with self.proxy_client:
            for token in tokens:
                pair = next((x for x in all_contracts if x.get("currency", "").lower() == token.base.lower()), None)
                if not pair:
                    continue
                chains = pair.get("chains", None)
                if not chains:
                    continue
                contract_addresses = tuple(i.get("addr", None) for i in chains if i.get("addr", None))
                if not contract_addresses:
                    continue
                await processor.add_task(lambda t=token, c=contract_addresses: self.__find_provider(t, c))
            await processor.run()

        if not self.dex_contracts:
            return
        print(f"pairs to cache: {len(self.dex_contracts)}")
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        await DexContractService.bulk_create_or_update(session, self.dex_contracts)
        await session_gen.aclose()

    async def get_cache_missing_symbols(self):
        mex_pairs = self.gate_client.get_all_pairs()
        symbols = [i.symbol for i in mex_pairs if i.symbol.split("_")[1] == "USDT"]
        cached_contracts = await self.__fetch_all_contracts(ClientType.GATE)
        cached_symbols = [i.symbol for i in cached_contracts if i.quote_token == "USDT"]
        missing_symbols = set(symbols).difference(set(cached_symbols))
        return missing_symbols

    @staticmethod
    async def __fetch_all_contracts(client_type: ClientType):
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        pairs = await DexContractService.fetch_all_contracts(session, client_type)
        await session_gen.aclose()
        return pairs

    def __get_all_contracts(self) -> list:
        return self.gate_client.all_currencies()

    async def __find_provider(self,
                              token: TokenPair,
                              contract_addresses: tuple[str]):
        all_pairs = []
        for contract_address in contract_addresses:
            url = f"https://api.dexscreener.com/latest/dex/search?q={contract_address}"
            async with self.semaphore:
                try:
                    response = await self.proxy_client.get(
                        url,
                        timeout=10,
                        processor=self.processor,
                        retry_callback=lambda: self.__find_provider(token, contract_addresses)
                    )

                    if response.status_code == 429:
                        return  # Proxy rotation handled by ProxyRotatingClient

                    response.raise_for_status()
                    pairs = response.json().get("pairs", None)
                    if not pairs:
                        continue
                    all_pairs.extend(pairs)
                except Exception as e:
                    print(f"Failed to fetch url {url}: {e}")
                    return

        dex_pairs = [PairDetails(**i) for i in all_pairs]
        try:
            sorted_pairs = sorted(
                dex_pairs,
                key=lambda p: float(p.liquidity.usd) if p.liquidity and p.liquidity.usd else 0.0,
                reverse=True
            )
            best = sorted_pairs[0] if sorted_pairs else None
            if best:
                contract = DexContract(chain_id=best.chainId, address=best.baseToken.address,
                                       symbol=token.symbol, base_token=token.base, quote_token=token.quote,
                                       client=ClientType.GATE)
                self.dex_contracts.append(contract)
                print(f"Found chain_id: {best.chainId} for address: {best.baseToken.address}")
        except Exception as e:
            print(f"Failed to fetch chain id: {e}")
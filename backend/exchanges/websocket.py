import asyncio
from .dexscreener.websocket import Websocket as <PERSON><PERSON><PERSON>so<PERSON>
from .mexc.websocket import Websocket as MexWebsocket
from .gate_io.websocket import Websocket as GateWebsocket
from models.pair import PriceComparison, PositionType
from .dexscreener.models import PairDetails
from .mexc.models import ContractTickerData
import time
import httpx
from utils.telegram_api import TelegramBotAPI
import logging
from .base import ExchangeApiClient, ClientType
from .base_websocket import BaseWebSocket
import os
from models.position_logger import PositionLogger, OperationType
from utils.services import LoggerService
from utils.database import get_session
from sqlalchemy.ext.asyncio import AsyncSession


class Websocket:
    DEX_MIN_FETCH_INTERVAL = 5

    def __init__(self, price_comparison: PriceComparison, client: ExchangeApiClient, close_callback):
        self.price_comparison = price_comparison
        self.client = client
        self.close_callback = close_callback

        self.dex_websocket = DexWebsocket(price_comparison.pair_details, callback_func=self.dex_callback)
        self.mex_websocket = self.__create_mex_websocket()

        self.dex_pair: PairDetails = price_comparison.pair_details
        self.mex_pair: ContractTickerData = price_comparison.contract_ticker

        self.dex_pair_last_update = time.time()
        self.diff_percent = price_comparison.diff_percent
        self.initial_diff_percent = price_comparison.diff_percent
        self.positions_info = None

        self.telegram = TelegramBotAPI()

    async def start(self):
        logging.info("Starting Websockets")
        self.telegram.send_message(self.client.client_type, 
                                   self.price_comparison, 
                                   self.initial_diff_percent, 
                                   position_type=self.__get_position_type())
        await asyncio.gather(
            self.dex_websocket.start(),
            self.mex_websocket.start(),
            self.__open_position()
        )

    async def close(self):
        logging.info("Closing Websockets")
        await asyncio.gather(
            self.dex_websocket.close(),
            self.mex_websocket.close(),
            self.__close_position()
        )
        await self.close_callback(self.identifier)

    async def dex_callback(self, pair: PairDetails):
        self.dex_pair = pair
        self.dex_pair_last_update = time.time()
        await self.compare()

    async def mex_callback(self, pair: ContractTickerData):
        self.mex_pair = pair
        should_update_dex_pair = time.time() - self.dex_pair_last_update > self.DEX_MIN_FETCH_INTERVAL
        if should_update_dex_pair:
            self.dex_pair = await self.__update_dex_pair()
            self.dex_pair_last_update = time.time()
        await self.compare()

    async def compare(self):
        price_comparison = PriceComparison(pair_details=self.dex_pair, contract_ticker=self.mex_pair)
        await self.__close_on_profit(price_comparison)
        await self.__close_on_stop_loss(price_comparison)

    async def __close_on_profit(self, price_comparison: PriceComparison):
        price_usd = float(price_comparison.pair_details.priceUsd)
        last_price = price_comparison.contract_ticker.lastPrice
        if not price_usd or not last_price:
            return
        difference = ((price_usd - last_price) / last_price) * 100
        if abs(difference) < 0.5:
            self.telegram.send_message(client_type=self.client.client_type,
                                       pair_data=price_comparison,
                                       difference=difference,
                                       should_close=True,
                                       message_id=self.telegram.message_id)
            await self.close()

    async def __close_on_stop_loss(self, price_comparison: PriceComparison):
        initial_cex_price = self.price_comparison.contract_ticker.lastPrice

        last_price = price_comparison.contract_ticker.lastPrice
        if not initial_cex_price or not last_price:
            return
        position = self.__get_position_type()
        price_change = ((last_price - initial_cex_price) / initial_cex_price) * 100

        should_exit = (
                (position == PositionType.LONG and price_change <= -2) or
                (position == PositionType.SHORT and price_change >= 2)
        )
        if should_exit:
            self.telegram.send_message(client_type=self.client.client_type,
                                       pair_data=price_comparison,
                                       difference=price_change,
                                       should_close=True,
                                       position_type=position,
                                       message_id=self.telegram.message_id)
            await self.close()

    def __create_mex_websocket(self) -> BaseWebSocket:
        random_user = self.client.get_random_user()
        if self.client.client_type == ClientType.MEXC:
            return MexWebsocket(random_user, self.price_comparison.contract_ticker, callback_func=self.mex_callback)
        return GateWebsocket(random_user, self.price_comparison.contract_ticker, callback_func=self.mex_callback)

    def __get_position_type(self) -> PositionType:
        initial_cex_price = self.price_comparison.contract_ticker.lastPrice or 0
        initial_dex_price = float(self.price_comparison.pair_details.priceUsd or 0)

        initial_difference = ((initial_dex_price - initial_cex_price) / initial_cex_price) * 100
        position = PositionType.LONG if initial_difference > 0 else PositionType.SHORT
        return position

    async def __open_position(self):
        debug = os.getenv("DEBUG") == "1"
        await self.__create_position_log(OperationType.OPEN)
        if debug:
            return
        self.positions_info = self.client.open_position_by_market(price_comparison=self.price_comparison,
                                                                  position_type=self.__get_position_type())

    async def __close_position(self):
        debug = os.getenv("DEBUG") == "1"
        if self.positions_info and not debug:
            self.client.close_position_by_market(self.positions_info)
        await self.__create_position_log(OperationType.CLOSE)

    async def __create_position_log(self, operation_type: OperationType):
        position_logger = PositionLogger(client_type=self.client.client_type,
                                         session_id=self.identifier,
                                         operation_type=operation_type,
                                         position_type=self.__get_position_type(),
                                         pair_details_json=self.price_comparison.pair_details.model_dump(),
                                         contract_ticker_data_json=self.price_comparison.contract_ticker.model_dump())
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        await LoggerService.create(session, position_logger)

    async def __update_dex_pair(self) -> PairDetails:
        url = f"https://api.dexscreener.com/latest/dex/pairs/{self.dex_pair.chainId}/{self.dex_pair.pairAddress}"
        try:
            async with httpx.AsyncClient(proxy="socks5h://127.0.0.1:9050") as client:
                response = await client.get(url)
                response.raise_for_status()
                json_data = response.json().get("pair")
                if json_data:
                    return PairDetails(**json_data)
                return self.dex_pair
        except Exception as e:
            logging.error(f"Failed to update DEX pair with reason: {e}")
            return self.dex_pair

    @property
    def identifier(self) -> str:
        return f"{self.dex_pair.identifier}_{self.client.name}"
from abc import ABC, abstractmethod

from .base import <PERSON>lient<PERSON>ser
from .mexc.models import ContractTickerData
import logging
import asyncio
import websockets
import json


class BaseWebSocket(ABC):
    def __init__(self, user: ClientUser, pair: ContractTickerData, callback_func):
        self.user = user
        self.symbol = pair.symbol
        self.websocket = None
        self.callback_func = callback_func
        self.is_connected = False

    @abstractmethod
    async def start(self):
        pass

    async def send_ping(self, websocket, payload: {str: any}, reconnect_callback=None):
        try:
            while websocket.open:
                await websocket.send(json.dumps(payload))
                await asyncio.sleep(20)
        except websockets.exceptions.ConnectionClosedOK:
            logging.info("[Ping] WebSocket closed normally (code 1000).")
            self.is_connected = False
        except websockets.exceptions.ConnectionClosedError as e:
            logging.error(f"[Ping] Abnormal close: {e.rcvd.code} — {e.rcvd.reason}")
            self.is_connected = False
            if reconnect_callback:
                await reconnect_callback()
        except Exception as e:
            logging.error(f"[Ping] Unexpected error: {e}")
            self.is_connected = False
            if reconnect_callback:
                await reconnect_callback()

    async def reconnect(self):
        logging.info("Reconnecting Mex WebSocket...")
        await asyncio.sleep(3)
        await self.start()

    async def close(self):
        logging.info("Closing Mex Websocket")
        self.is_connected = False
        await self.websocket.close(code=1000, reason="Normal closure")

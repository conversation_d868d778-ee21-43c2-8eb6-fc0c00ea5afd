from .models import PairDetails
import os
import base64
import websockets
import httpx
import asyncio
import logging

class Websocket:
    def __init__(self, dex_pair: PairDetails, callback_func):
        self.chain_id = dex_pair.chainId
        self.pair_address = dex_pair.pairAddress
        self.cached_response = None
        self.websocket = None
        self.callback_func = callback_func

    async def start(self):
        retries = 0
        max_retries = 10
        url = f"wss://io.dexscreener.com/dex/screener/v5/pair/{self.chain_id}/{self.pair_address}"
        try:
            async with websockets.connect(url, extra_headers=self.__headers()) as websocket:
                self.websocket = websocket
                while retries < max_retries:
                    response = await websocket.recv()
                    if response == "ping":
                        continue
                    if isinstance(response, bytes):
                        int8_array = list(
                            int.from_bytes(response[i:i + 1], "big", signed=True) for i in range(len(response)))
                        if int8_array != self.cached_response:
                            self.cached_response = int8_array
                            await self.__update_dex_pair()
        except Exception as e:
            code = getattr(e, "code", None)
            if code == 1000:
                logging.info("[DEX Websocket] Normal closure.")
                return
            logging.error(f"[DEX Websocket]: Reason: {e}")
            await asyncio.sleep(3)

    @staticmethod
    def __generate_sec_websocket_key():
        random_bytes = os.urandom(16)
        key = base64.b64encode(random_bytes).decode('utf-8')
        return key

    def __headers(self):
        return {
            "Host": "io.dexscreener.com",
            "Connection": "Upgrade",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Upgrade": "websocket",
            "Origin": "https://dexscreener.com",
            "Sec-WebSocket-Version": 13,
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7",
            "Sec-WebSocket-Key": self.__generate_sec_websocket_key()
        }

    async def __update_dex_pair(self):
        url = f"https://api.dexscreener.com/latest/dex/pairs/{self.chain_id}/{self.pair_address}"
        async with httpx.AsyncClient(proxy="socks5h://127.0.0.1:9050") as client:
            response = await client.get(url)
            response.raise_for_status()
            json_data = response.json().get("pair")
            if json_data:
                dex_pair = PairDetails(**json_data)
                await self.callback_func(dex_pair)

    async def close(self):
        logging.info("Closing Dex Websocket")
        await self.websocket.close(code=1000, reason="Normal closure")

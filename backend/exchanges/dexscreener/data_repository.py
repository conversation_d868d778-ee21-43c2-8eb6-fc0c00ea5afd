from exchanges.mexc.models import ContractTickerData
from utils.database import get_session
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from .models import PairDetails
from .dex_pair_utils import DexPairValidator
from utils.services import DexContractService
from models.dex_pair import DexContract
from ..base import ClientType
from utils.proxy_rotating_client import ProxyRotating<PERSON>lient, AsyncQueueProcessor


class Repository:
    def __init__(self):
        self.base_url = "https://api.dexscreener.com"
        self.semaphore = asyncio.Semaphore(10)
        self.processor = None
        self.__fetched_pais: list[PairDetails] = []
        self.__dex_contracts: list[DexContract] = []
        self.dex_pair_validator = DexPairValidator()
        self.proxy_client = ProxyRotatingClient()

    async def get_dex_pairs(self, client_type: ClientType) -> list[PairDetails]:
        pairs_by_chain_id = await self.get_pairs_grouped_by_chain_id(client_type)
        urls = self.build_token_urls(pairs_by_chain_id)

        processor = AsyncQueueProcessor(max_concurrent_tasks=10)
        self.processor = processor

        async with self.proxy_client:
            for url in urls:
                await processor.add_task(lambda u=url: self.__process_dex_url(u))
            await processor.run()
        return self.__fetched_pais

    async def __process_dex_url(self, url: str):
        async with self.semaphore:
            try:
                response = await self.proxy_client.get(
                    url,
                    timeout=10,
                    processor=self.processor,
                    retry_callback=lambda: self.__process_dex_url(url)
                )

                if response.status_code == 429:
                    return  # Proxy rotation handled by ProxyRotatingClient

                response.raise_for_status()
                dex_pairs = response.json()
                dex_pairs_models = [
                    pair for i in dex_pairs
                    if DexPairValidator.validate(pair := PairDetails(**i))
                ]
                self.__fetched_pais.extend(dex_pairs_models)
            except Exception as e:
                # print(f"Failed to fetch url {url}: {e}")
                print(f"Failed to fetch url {e}")
                return

    def build_token_urls(self,
                         tokens_by_chain: dict[str, list[str]],
                         max_addresses: int = 30) -> list[str]:
        urls = []

        for chain_id, addresses in tokens_by_chain.items():
            from urllib.parse import quote
            unique_addresses = list(set(addresses))
            unique_addresses = [quote(i, safe='') for i in unique_addresses]

            for i in range(0, len(unique_addresses), max_addresses):
                batch = unique_addresses[i:i + max_addresses]
                batch_str = ",".join(batch)
                url = f"{self.base_url}/tokens/v1/{chain_id}/{batch_str}"
                urls.append(url)

        return urls

    @staticmethod
    async def get_pairs_grouped_by_chain_id(client_type: ClientType) -> dict[str, list[str]]:
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        pairs = await DexContractService.get_pairs_grouped_by_chain_id(session, client_type)
        await session_gen.aclose()
        return pairs

    async def cache_pairs(self, mex_pairs: list[ContractTickerData]):
        processor = AsyncQueueProcessor(max_concurrent_tasks=10)
        self.processor = processor

        async with self.proxy_client:
            for pair in mex_pairs:
                await processor.add_task(lambda p=pair: self.__process_pair(p))
            await processor.run()

        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        await DexContractService.bulk_create(session, self.__dex_contracts)
        await session_gen.aclose()

    async def __process_pair(self, mex_pair: ContractTickerData):
        base_token, quote_token = mex_pair.symbol.split("_")
        url = f"{self.base_url}/latest/dex/search"
        params = {"q": base_token}

        async with self.semaphore:
            try:
                response = await self.proxy_client.get(
                    url,
                    params=params,
                    timeout=10.0,
                    processor=self.processor,
                    retry_callback=lambda: self.__process_pair(mex_pair)
                )

                if response.status_code == 429:
                    return  # Proxy rotation handled by ProxyRotatingClient

                response.raise_for_status()
                json_data = response.json().get("pairs", [])
                dex_pairs = [
                    pair for i in json_data
                    if (pair := PairDetails(**i)).liquidity is not None
                ]
                pair_details = self.dex_pair_validator.filter_pairs(dex_pairs, mex_pair)
                if pair_details:
                    dex_contract = DexContract(chain_id=pair_details.chainId, address=pair_details.pairAddress,
                                               symbol=pair_details.pair_symbol, base_token=pair_details.baseToken.symbol,
                                               quote_token=pair_details.quoteToken.symbol,
                                               client=ClientType.MEXC)
                    self.__dex_contracts.append(dex_contract)
            except Exception as e:
                print(f"Failed to fetch {base_token}: {e}")
                return
            finally:
                print(f"[EXIT] {base_token}")
import logging

from .models import PairDetails
from exchanges.mexc.models import ContractTickerData
from models.pair import PriceComparison
from config import (
    MAX_PRICE_DIFFERENCE_PCT,
    MAJOR_DEX_PROVIDERS,
    MIN_DEX_LIQUIDITY_USD,
    MIN_DEX_VOLUME_USD,
    MIN_DEX_MARKET_CAP_USD,
)


class DexPairValidator:
    @staticmethod
    def filter_pairs(pairs: list[PairDetails], mexc_pair: ContractTickerData) -> PairDetails | None:
        print(f"[INFO] filter pairs for symbol: {mexc_pair.symbol}")

        print(f"[INFO] cex_price: {mexc_pair.lastPrice}")
        base_token = mexc_pair.symbol.split('_')[0]
        grouped = {}
        for pair in pairs:
            if pair.dexId.lower() not in MAJOR_DEX_PROVIDERS:
                print(f"[INFO] Skipping pair {pair} due to not in major dex providers")
                continue

            if pair.liquidity.usd <= MIN_DEX_LIQUIDITY_USD:
                print(f"[INFO] Skipping pair {pair} due to liquidity issues")
                continue

            if pair.baseToken.symbol.upper() != base_token.upper():
                print(f"[INFO] Skipping pair {pair} due to base token mismatch")
                continue

            if -0.1 < pair.priceChange.h24 < 0.1:
                print(f"[INFO] Skipping pair {pair} due to high price change ({pair.priceChange.h24})")
                continue

            if pair.volume.h24 < MIN_DEX_VOLUME_USD:
                continue

            if pair.marketCap < MIN_DEX_MARKET_CAP_USD:
                continue

            dex_id = pair.dexId.lower()
            grouped.setdefault(dex_id, []).append(pair)

        print(f"[INFO] Found {len(grouped)} DEXs with valid pairs for {base_token}")
        best_pairs = []
        for dex_id, dex_pairs in grouped.items():
            sorted_pairs = sorted(
                dex_pairs,
                key=lambda p: float(p.liquidity.usd) if p.liquidity and p.liquidity.usd else 0.0,
                reverse=True
            )
            best_pairs.append(sorted_pairs[0])

        scored_pairs = []
        if mexc_pair.lastPrice:
            for pair in best_pairs:
                price_float = float(pair.priceUsd or 0)
                if price_float:
                    price_diff_pct = abs((price_float - mexc_pair.lastPrice) / mexc_pair.lastPrice) * 100

                    if price_diff_pct <= MAX_PRICE_DIFFERENCE_PCT:
                        scored_pairs.append((pair, price_diff_pct))
                        print(f"{pair.baseToken.symbol}/{pair.quoteToken.symbol} on {pair.dexId} "
                            f"diff: {price_diff_pct:.2f}% (CEX: ${mexc_pair.lastPrice}, DEX: ${price_float})")
            if scored_pairs:
                scored_pairs.sort(key=lambda x: x[1])  # Smallest price diff
                return scored_pairs[0][0]

            print(f"No suitable pairs matched CEX price threshold ({MAX_PRICE_DIFFERENCE_PCT}%)")
            return None

        return max(scored_pairs, key=lambda p: (p.liquidity.usd, p.marketCap))

    @staticmethod
    def filter_matching_pairs(mexc_pairs: list[ContractTickerData],
                              dex_pairs: list[PairDetails]) -> list[PriceComparison]:
        if not mexc_pairs:
            logging.error("❌ Failed to fetch MEXC pairs, got empty empty list")
            return []
        if not dex_pairs:
            logging.error("❌ Failed to fetch DEX pairs, got empty empty list")
            return []

        ticker_map = {t.base_token: t for t in mexc_pairs}
        matched = [
            (ticker_map[p.baseToken.symbol], p)
            for p in dex_pairs
            if p.baseToken.symbol and p.baseToken.symbol in ticker_map
        ]

        valid_pairs = []
        for match in matched:
            price_comparison = PriceComparison(
                pair_details=match[1],
                contract_ticker=match[0]
            ).compare()
            if price_comparison:
                valid_pairs.append(price_comparison)

        return valid_pairs

    @staticmethod
    def validate(pair: PairDetails) -> bool:
        try:
            if pair.liquidity.usd <= MIN_DEX_LIQUIDITY_USD:
                return False

            if -0.1 < pair.priceChange.h24 < 0.1:
                return False

            if pair.volume.h24 < MIN_DEX_VOLUME_USD:
                return False

            if pair.marketCap < MIN_DEX_MARKET_CAP_USD:
                return False
            return True
        except Exception as e:
            return False
from pydantic import BaseModel, PrivateAttr
from typing import Any

class Token(BaseModel):
    address: str
    name: str
    symbol: str

class PriceChange(BaseModel):
    h24: float | None = 0.0

class Volume(BaseModel):
    h24: float | None = 0.0

class Liquidity(BaseModel):
    usd: float | None = 0.0
    base: float | None = 0.0
    quote: float | None = 0.0

class PairDetails(BaseModel):
    chainId: str | None = None
    dexId: str | None = None
    url: str | None = None
    pairAddress: str | None = None
    baseToken: Token | None = None
    quoteToken: Token | None = None
    priceNative: str | None = None
    priceUsd: str | None = None
    priceChange: PriceChange | None = None
    liquidity: Liquidity | None = None
    volume: Volume | None = None
    fdv: float | None = 0.0
    marketCap: float | None = 0.0
    pairCreatedAt: int | None = 0

    _original_json: dict[str, Any] = PrivateAttr()

    def __init__(self, **data):
        super().__init__(**data)
        self._original_json = data.copy()

    @property
    def pair_symbol(self) -> str:
        """Returns the normalized pair symbol (e.g., 'APX_USDT'), or None if incomplete data."""
        base = self.baseToken.symbol
        quote = self.quoteToken.symbol
        return f"{base.upper()}_{quote.upper()}"

    @property
    def identifier(self) -> str:
        return f"{self.chainId}_{self.pairAddress}"

    def __str__(self):
        return f"""
        {{
            chainId: {self.chainId},
            dexId: {self.dexId},
            pair_symbol: {self.pair_symbol},
            priceChange: {self.priceChange}
        }}
        """
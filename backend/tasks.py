from celery_app import celery_app
import asyncio
from pairs_cache import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from entry_point import EntryPoint

@celery_app.task(name="update_dex_pairs")
def update_dex_pairs(**data):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    cacher = PairAddressCacher()
    loop.run_until_complete(cacher.run_cache())
    loop.close()

@celery_app.task(name="sync_pairs")
def sync_pairs(**data):
    entry_point = EntryPoint()
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(entry_point.sync_pirs())
    loop.close()

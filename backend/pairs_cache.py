from exchanges.dexscreener.data_repository import Repository
import asyncio
from utils.database import create_db_and_tables
from exchanges.mexc.client import MexcClient
from exchanges.gate_io.client import GateIOClient
import time
from utils.format_duration import format_duration
from exchanges.mexc.contracts_cacher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>acher
from exchanges.gate_io.contracts_cacher import GatePairCacher


class PairAddressCacher:
    def __init__(self):
        self.dex_remote_repository = Repository()

    async def run_cache(self):
        start = time.time()
        await create_db_and_tables()
        await self.run_mex_cache()
        await self.run_gate_cache()
        print("✅ Dex pairs updated ✅")
        end = time.time()
        print(f"Total time: {format_duration(end - start)}")


    async def run_mex_cache(self):
        client = MexcClient()
        pair_cacher = MexcPairCacher(client)
        await pair_cacher.cache_pairs()
        missing_symbols = await pair_cacher.get_cache_missing_symbols()
        pairs = client.get_all_pairs()
        pairs = [i for i in pairs if i.symbol in missing_symbols]
        await self.dex_remote_repository.cache_pairs(pairs)  # search for missing pairs amd cache addresses


    async def run_gate_cache(self):
        client = GateIOClient()
        pair_cacher = GatePairCacher(client)
        await pair_cacher.cache_pairs()
        missing_symbols = await pair_cacher.get_cache_missing_symbols()
        pairs = client.get_all_pairs()
        pairs = [i for i in pairs if i.symbol in missing_symbols]
        await self.dex_remote_repository.cache_pairs(pairs)  # search for missing pairs amd cache addresses


if __name__ == "__main__":
    cacher = PairAddressCacher()
    asyncio.run(cacher.run_cache())

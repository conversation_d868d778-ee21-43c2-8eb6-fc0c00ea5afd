"""Configuration settings for the application"""

# API Endpoints
MEXC_BASE_URL = "https://contract.mexc.com"
GATEIO_BASE_URL = "https://api.gateio.ws"
DEXSCREENER_BASE_URL = "https://api.dexscreener.io/latest/dex/"

# Tokens to ignore in pair matching
BASE_TOKENS_TO_IGNORE = ["BTC", "ETH", "LTC", "USDT", "USDC", "EUR", "GBP", "SOL", "BNB", "BCH", "ETC", "DYDX"]

# Minimum price difference to report (percentage)
MIN_PRICE_DIFFERENCE_PCT = 3.0

# Maximum allowed price difference between MEXC and DEXScreener (percentage)
MAX_PRICE_DIFFERENCE_PCT = 30.0  # 30%

# Minimum liquidity and volume filters
MIN_DEX_LIQUIDITY_USD = 30000
MIN_DEX_VOLUME_USD = 30000
MIN_CEX_VOLUME_USD = 30000
MIN_DEX_MARKET_CAP_USD = 50000


# List of major DEX providers to filter by (lowercase for case-insensitive comparison)
MAJOR_DEX_PROVIDERS = [
    'pumpswap',
    'raydium',
    'meteora',
    'orca',
    'uniswap',
    'stonfi',
    'pancakeswap',
    'cetus',
    'sushiswap',
    'quickswap' 
]

TG_BOT_TOKEN = '**********:AAGCApYbdTjDKMy8WLNtV9Mr_xEl3XqaTAU'
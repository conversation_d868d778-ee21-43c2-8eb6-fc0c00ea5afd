import httpx
import asyncio

async def test_tor():
    async with httpx.AsyncClient(proxy="socks5h://127.0.0.1:9050") as client:
        r = await client.get("https://check.torproject.org/api/ip", timeout=10)
        print("Tor IP:", r.json())

async def switch_tor_identity() -> httpx.AsyncClient:
    try:
        reader, writer = await asyncio.open_connection("127.0.0.1", 9051)

        writer.write(b'AUTHENTICATE ""\r\n')
        await writer.drain()

        writer.write(b'SIGNAL NEWNYM\r\n')
        await writer.drain()

        writer.write(b'QUIT\r\n')
        await writer.drain()

        writer.close()
        await writer.wait_closed()

        print("🔁 Tor identity changed (SIGNAL NEWNYM)")
    except Exception as e:
        print(f"❌ Failed to switch Tor identity: {e}")
    return httpx.AsyncClient(proxy="socks5h://127.0.0.1:9050")
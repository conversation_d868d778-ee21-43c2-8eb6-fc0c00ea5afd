import requests
from dataclasses import dataclass
import random


@dataclass
class Proxy:
    ip: str
    port: str
    username: str
    password: str

    @property
    def value(self) -> str:
        return f"http://{self.username}:{self.password}@{self.ip}:{self.port}"

class ProxyFetcher:
    @staticmethod
    def fetch_proxies() -> list[Proxy]:
        headers = {
            "Authorization": "Token qxgdypm4bllzu8bvwlvmqqbktvzfqwlo7hsg8w2q"
        }
        url = "https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page=1&page_size=25"
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        result = response.json().get("results", None)
        if not result:
            return []

        proxies = []
        for item in result:
            proxy = Proxy(
                ip=item.get("proxy_address"),
                port=item.get("port"),
                username=item.get("username"),
                password=item.get("password")
            )
            proxies.append(proxy)
        return proxies

    @property
    def random_proxy(self) -> Proxy:
        return random.choice(self.fetch_proxies())

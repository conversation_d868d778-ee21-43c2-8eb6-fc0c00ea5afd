import requests
from models.pair import <PERSON><PERSON>omparison, PositionType
import json
from exchanges.base import ClientType
from config import TG_BOT_TOKEN
import os


class TelegramBotAPI:
    def __init__(self):
        self.base_url = f"https://api.telegram.org/bot{TG_BOT_TOKEN}"
        self.message_id: str | None = None

    def send_message(self, client_type: ClientType,
                     pair_data: PriceComparison,
                     difference: float,
                     parse_mode: str = "HTML",
                     should_close: bool = False,
                     position_type: PositionType = None,
                     message_id: str = None):
        debug = os.getenv("DEBUG") == "1"
        if debug:
            print("🛠️ Not sending the message because Debug mode is ON.")
            return

        url = f"{self.base_url}/sendMessage"

        message = self.__create_message(client_type,
                                        pair_data,
                                        difference,
                                        should_close,
                                        position_type,
                                        message_id)
        payload = {
            "chat_id": client_type.tg_chat_id,
            "text": message,
            "parse_mode": parse_mode
        }
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            result = response.json().get("result", {})
            self.message_id = result.get("message_id")
        except requests.exceptions.HTTPError as err:
            print(f"❌ Failed to send to {client_type.tg_chat_id}: {err}")
            raise err


    def __create_message(self,
                         client_type: ClientType,
                         pair_data: PriceComparison,
                         difference: float,
                         should_close: bool = False,
                         position_type: PositionType = None,
                         message_id: str = None) -> str:
        def html_escape(text: str) -> str:
            if text is None:
                return ""
            return str(text).replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

        def four_spaces() -> str:
            return "    "

        mex_data = pair_data.contract_ticker
        dex_data = pair_data.pair_details

        mex_price = float(mex_data.lastPrice or 0)
        dex_price = float(dex_data.priceUsd or 0)

        client_name = client_type.name
        if position_type:
            position = f"{position_type.direction} on {client_name}" 

        dex_url = html_escape(dex_data.url)
        client_url = self.__get_client_url(client_type, mex_data.symbol)

        lines = [
            f'<a href="{dex_url}">DEX Screener</a> | <a href="{client_url}">{client_name}</a>',
            f"<b>Price:</b>",
            f"{four_spaces()}<b>DEX:</b> <code>${dex_price}</code>",
            f"{four_spaces()}<b>{client_name}:</b> <code>${mex_price}</code>",
            f"<b>Difference:</b>",
            f"{four_spaces()}<b>Current:</b> <code>{round(difference, 2)}%</code>",
        ]

        if message_id:
            message_link = f"https://t.me/c/2836712464/{message_id}"
            first_line = f'<a href="{message_link}"><b>{html_escape(dex_data.pair_symbol)}</b></a>'
        else:
            first_line = f'<b>{html_escape(dex_data.pair_symbol)}</b>'
        lines.insert(0, first_line)

        if should_close:
            if position_type:
                lines.append(f"<b>❌ 🔻{mex_data.symbol} stop loss ❌</b>")
            else:
                lines.append(f"<b>🔻{mex_data.symbol} prices have leveled off to {round(difference, 2)}%</b>")
        else:
            lines.append(f"<b>Suggested Position:</b> {html_escape(position)}")

        lines += [
            f"<b>Market Cap:</b> <code>${html_escape(str(dex_data.marketCap))}</code>",
            f"<b>Liquidity:</b> <code>${html_escape(str(dex_data.liquidity.usd))}</code>",
            f"<b>DexId:</b> <code>{html_escape(dex_data.dexId)}</code>",
            f"<b>ChainId:</b> <code>{html_escape(dex_data.chainId)}</code>",
            f"<b>PriceChange 24H:</b> <code>{html_escape(str(dex_data.priceChange.h24))}%</code>",
        ]

        return "\n".join(lines)

    @staticmethod
    def __get_client_url(client_type: ClientType, symbol: str) -> str:
        match client_type:
            case ClientType.MEXC:
                return f"https://www.mexc.com/exchange/{symbol}"
            case ClientType.GATE:
                return f"https://www.gate.com/futures/USDT/{symbol}"
            case _:
                raise Exception(f"Unknown client type: {client_type}")

    def get_private_channel_chat_ids(self) -> list[str]:
        url = f'{self.base_url}/getUpdates'
        channel_ids = set()
        try:
            response = requests.get(url)
            updates = response.json()
            print("Bot trying to get channels", json.dumps(updates, indent=4))
            for update in updates.get('result', []):
                if 'channel_post' in update:
                    chat = update['channel_post']['chat']
                    if chat.get('type') == 'channel':
                        channel_ids.add(chat.get('id'))

                elif 'my_chat_member' in update:
                    chat = update['my_chat_member']['chat']
                    if chat.get('type') == 'channel':
                        channel_ids.add(chat.get('id'))
            return list(channel_ids)
        except Exception as e:
            print(f"Error fetching updates: {e}")
            return []

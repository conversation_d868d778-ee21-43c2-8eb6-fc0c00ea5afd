from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel
from models.dex_pair import DexPair, DexContract, Base


DATABASE_URL = "postgresql+asyncpg://cgPQKsE3cSykEKy92p1R:dVHZNWUZLL9jV6pP5WZj@postgres:5432/screaner"


def create_engine_and_session():
    engine = create_async_engine(
        DATABASE_URL,
        echo=False,
        pool_size=20,
        max_overflow=30,
        pool_pre_ping=True,
        pool_recycle=3600,
        pool_timeout=30,
    )
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    return engine, async_session


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    engine, async_session = create_engine_and_session()
    async with async_session() as session:
        yield session
    await engine.dispose()


async def create_db_and_tables():
    engine, _ = create_engine_and_session()
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)
        await conn.run_sync(Base.metadata.create_all)
    await engine.dispose()


async def reset_dex_pairs_table():
    engine, _ = create_engine_and_session()
    async with engine.begin() as conn:
        print("🚨 Dropping dex_pairs table...")
        await conn.run_sync(DexPair.__table__.drop)
        print("✅ Recreating dex_pairs table...")
        await conn.run_sync(DexPair.__table__.create)
        print("✅ dex_pairs table reset complete")
    await engine.dispose()

async def reset_dex_contracts_table():
    engine, _ = create_engine_and_session()
    async with engine.begin() as conn:
        print("🚨 Dropping dex_contract table...")
        await conn.run_sync(lambda conn: DexContract.__table__.drop(conn, checkfirst=True))
        print("✅ Recreating dex_contract table...")
        await conn.run_sync(lambda conn: DexContract.__table__.create(conn, checkfirst=True))
        print("✅ dex_contract table reset complete")
    await engine.dispose()

if __name__ == "__main__":
    import asyncio
    asyncio.run(reset_dex_pairs_table())
    asyncio.run(reset_dex_contracts_table())
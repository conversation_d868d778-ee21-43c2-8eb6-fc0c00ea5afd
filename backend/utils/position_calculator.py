
from models.pair import PriceComparison, PositionType

RISK_PERCENT = 0.01  # 1% risk per trade
STOPLOSS_PERCENT = 0.02  # 2% stoploss
LEVERAGE = 20  # Maximum leverage allowed
SAFETY_MARGIN = 0.95  # Use 95% of available balance to account for fees and slippage


class PositionCalculator:
    def __init__(self,
                 price_comparison: PriceComparison,
                 position_type: PositionType,
                 balance: float,
                 quanto_multiplier: float,
                 leverage_max: int,
                 contract_min_qty: float,
                 contract_max_qty: float,
                 maintenance_rate: float):
        self.price_comparison = price_comparison
        self.balance = balance
        self.quanto_multiplier = self.multiplier(quanto_multiplier)
        self.leverage_max = leverage_max
        self.position_type = position_type
        self.maintenance_rate = maintenance_rate
        self.contract_min_qty = contract_min_qty
        self.contract_max_qty = contract_max_qty

    def calculate_stoploss(self) -> float:
        entry_price = self.price_comparison.contract_ticker.lastPrice
        return entry_price * (1 - STOPLOSS_PERCENT) 
    # if self.position_type == PositionType.LONG else entry_price * (1 + STOPLOSS_PERCENT)

    def multiplier(self, quanto_multiplier) -> float:
        # Don't cap the multiplier at 1 - use the actual value from the exchange
        return quanto_multiplier if quanto_multiplier > 0 else 1.0
    
    def calculate_risk_amount(self) -> float:
        return self.balance * RISK_PERCENT

    def calculate_position_size(self) -> int:

        leverage = self.leverage_max

        if any(x <= 0 for x in [self.balance, leverage, self.quanto_multiplier]):
            raise ValueError("Balance, leverage, and quanto multiplier must be positive.")

        # Get entry price, stop-loss, and risk amount
        entry_price = self.price_comparison.contract_ticker.lastPrice
        stop_loss_price = self.calculate_stoploss()
        risk_amount = self.calculate_risk_amount()

        # Validate prices
        if entry_price <= 0 or stop_loss_price <= 0:
            raise ValueError("Entry and stop-loss prices must be positive.")
        if self.position_type == PositionType.LONG and stop_loss_price >= entry_price:
            raise ValueError(f"Stop-loss ({stop_loss_price}) must be below entry price ({entry_price}) for long position.")
        if self.position_type == PositionType.SHORT and stop_loss_price <= entry_price:
            raise ValueError(f"Stop-loss ({stop_loss_price}) must be above entry price ({entry_price}) for short position.")

        # Calculate risk per unit
        risk_per_unit = entry_price - stop_loss_price if self.position_type == PositionType.LONG else stop_loss_price - entry_price

        # Calculate contract value (notional value per contract)
        contract_value = entry_price * self.quanto_multiplier

        # Check for invalid values
        if risk_per_unit <= 0 or contract_value <= 0:
            raise ValueError("Invalid stop loss or quanto multiplier.")

        # Position size based on risk management
        position_size_by_risk = risk_amount / (risk_per_unit * self.quanto_multiplier)

        # Available balance for margin (apply safety margin)
        available_balance = self.balance * SAFETY_MARGIN

        # Calculate maximum position size based on available margin
        # Margin required per contract = contract_value / leverage
        margin_per_contract = contract_value / leverage
        max_contracts_by_balance = available_balance / margin_per_contract

        # Take the minimum of risk-based and balance-based position sizes
        position_size = min(position_size_by_risk, max_contracts_by_balance)

        # Apply contract size constraints
        position_size = max(self.contract_min_qty, min(position_size, self.contract_max_qty))

        # Final verification - ensure we don't exceed available balance
        total_margin_required = position_size * margin_per_contract
        if total_margin_required > available_balance:
            # Recalculate position size to fit within available balance
            position_size = available_balance / margin_per_contract
            position_size = max(self.contract_min_qty, min(position_size, self.contract_max_qty))

        # Round down to integer and ensure minimum position
        final_position_size = int(position_size)

        # Final safety check
        final_margin = final_position_size * margin_per_contract
        if final_margin > available_balance:
            # If still too large, reduce by 1 contract
            final_position_size = max(0, final_position_size - 1)

        print(f"Position calculation details:")
        print(f"  Entry price: {entry_price}")
        print(f"  Contract value: {contract_value}")
        print(f"  Leverage: {leverage}")
        print(f"  Available balance: {available_balance:.6f}")
        print(f"  Margin per contract: {margin_per_contract:.6f}")
        print(f"  Max contracts by balance: {max_contracts_by_balance:.6f}")
        print(f"  Position size by risk: {position_size_by_risk:.6f}")
        print(f"  Final position size: {final_position_size}")
        print(f"  Final margin required: {final_position_size * margin_per_contract:.6f}")

        return final_position_size

    # def calculate_position_size(self):
    #     entry_price = self.price_comparison.contract_ticker.lastPrice
    #     stop_loss_price = self.calculate_stoploss()
    #     risk_amount = self.calculate_risk_amount()
    #     risk_per_unit = abs(entry_price - stop_loss_price)
    #     contract_value = entry_price * self.quanto_multiplier

    #     if risk_per_unit == 0 or contract_value == 0:
    #         raise ValueError("Invalid stop loss or quanto multiplier")

    #     # Calculate position size based on risk
    #     position_size_by_risk = risk_amount / (risk_per_unit * contract_value)

    #     # Calculate position size based on max allowable margin
    #     max_contracts_by_balance = (self.balance * self.leverage_max) / contract_value

    #     # Use the lower of the two
    #     position_size = min(position_size_by_risk, max_contracts_by_balance)

    #     return max(1, int(position_size))
    # def calculate_position_size(self):
    #     entry_price = self.price_comparison.contract_ticker.lastPrice
    #     stop_loss_price = self.calculate_stoploss()
    #     risk_amount = self.calculate_risk_amount()  # B * R
    #     risk_per_unit = abs(entry_price - stop_loss_price)  # P_e * S
    #     contract_value = entry_price * self.quanto_multiplier  # P_e * Q

    #     if risk_per_unit == 0 or contract_value == 0:
    #         raise ValueError("Invalid stop loss or quanto multiplier")

    #     # Position size: C = (B * R) / (Q * P_e * S)
    #     position_size_by_risk = risk_amount / (self.quanto_multiplier * risk_per_unit)
    #     max_contracts_by_balance = (self.balance * self.leverage_max) / contract_value
    #     position_size = min(position_size_by_risk, max_contracts_by_balance)
    #     return max(1, int(position_size))

    def calculate_margin(self, is_long: bool = True):
        """Calculate the margin required for the position."""
        contracts = self.calculate_position_size(is_long)
        entry_price = self.price_comparison.contract_ticker.lastPrice
        contract_value = entry_price * self.quanto_multiplier
        margin_required = contracts * (contract_value / self.leverage_max)
        return margin_required

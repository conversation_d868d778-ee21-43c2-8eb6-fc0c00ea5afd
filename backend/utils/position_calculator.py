
from models.pair import PriceComparison

RISK_PERCENT = 0.01  # 2% risk per trade
STOPLOSS_PERCENT = 0.02  # 2% stoploss
LEVERAGE = 20  # Maximum leverage allowed


class PositionCalculator:
    def __init__(self,
                 price_comparison: PriceComparison,
                 balance: float,
                 quanto_multiplier: float,
                 leverage_max: int,
                 contract_min_qty: float,
                 contract_max_qty: float,
                 maintenance_rate: float):
        self.price_comparison = price_comparison
        self.balance = balance
        self.quanto_multiplier = self.multiplier(quanto_multiplier)
        self.leverage_max = leverage_max
        self.maintenance_rate = maintenance_rate
        self.contract_min_qty = contract_min_qty
        self.contract_max_qty = contract_max_qty

    def calculate_stoploss(self) -> float:
        entry_price = self.price_comparison.contract_ticker.lastPrice
        return entry_price * (1 - STOPLOSS_PERCENT)

    def  multiplier(self, quanto_multiplier) -> float:
        return min(1, quanto_multiplier)
    
    def calculate_risk_amount(self) -> float:
        return self.balance * RISK_PERCENT

    def calculate_position_size(self, is_long: bool = True) -> int:
        """
        Calculate the number of contracts for a QNT/USDT futures position based on risk and margin.

        Args:
            leverage (float, optional): Leverage for the trade (e.g., 20 for 20x). Defaults to self.leverage_max.
            is_long (bool): True for long position, False for short. Defaults to True.

        Returns:
            int: Number of contracts (rounded down).

        Raises:
            ValueError: If inputs are invalid (e.g., negative values, zero risk per unit).
        """
        # Use provided leverage or fall back to leverage_max
        leverage = self.leverage_max

        if any(x <= 0 for x in [self.balance, leverage, self.quanto_multiplier]):
            raise ValueError("Balance, leverage, and quanto multiplier must be positive.")
        if leverage > self.leverage_max:
            raise ValueError(f"Leverage ({leverage}) cannot exceed max leverage ({self.leverage_max}).")

        # Get entry price, stop-loss, and risk amount
        entry_price = self.price_comparison.contract_ticker.lastPrice
        stop_loss_price = self.calculate_stoploss()  # TODO: Replace with 0.0436982 if calculate_stoploss fails
        risk_amount = self.calculate_risk_amount()

        # Validate prices
        if entry_price <= 0 or stop_loss_price <= 0:
            raise ValueError("Entry and stop-loss prices must be positive.")
        if is_long and stop_loss_price >= entry_price:
            raise ValueError(f"Stop-loss ({stop_loss_price}) must be below entry price ({entry_price}) for long position.")
        if not is_long and stop_loss_price <= entry_price:
            raise ValueError(f"Stop-loss ({stop_loss_price}) must be above entry price ({entry_price}) for short position.")

        # Calculate risk per unit
        risk_per_unit = entry_price - stop_loss_price if is_long else stop_loss_price - entry_price

        # Calculate contract value
        contract_value = entry_price * self.quanto_multiplier

        # Check for invalid values
        if risk_per_unit == 0 or contract_value == 0:
            raise ValueError("Invalid stop loss or quanto multiplier.")

        # Position size based on risk
        position_size_by_risk = risk_amount / (risk_per_unit * self.quanto_multiplier)

        # Position size based on margin
        max_contracts_by_balance = (self.balance * leverage) / contract_value

        # Take the minimum and round down
        position_size = min(position_size_by_risk, max_contracts_by_balance)

        # Verify total margin
        total_margin = position_size * (contract_value / leverage)
        if total_margin > self.balance:
            raise ValueError(f"Required margin ({total_margin:.2f} USDT) exceeds balance ({self.balance:.2f} USDT).")

        return int(position_size)

    # def calculate_position_size(self):
    #     entry_price = self.price_comparison.contract_ticker.lastPrice
    #     stop_loss_price = self.calculate_stoploss()
    #     risk_amount = self.calculate_risk_amount()
    #     risk_per_unit = abs(entry_price - stop_loss_price)
    #     contract_value = entry_price * self.quanto_multiplier

    #     if risk_per_unit == 0 or contract_value == 0:
    #         raise ValueError("Invalid stop loss or quanto multiplier")

    #     # Calculate position size based on risk
    #     position_size_by_risk = risk_amount / (risk_per_unit * contract_value)

    #     # Calculate position size based on max allowable margin
    #     max_contracts_by_balance = (self.balance * self.leverage_max) / contract_value

    #     # Use the lower of the two
    #     position_size = min(position_size_by_risk, max_contracts_by_balance)

    #     return max(1, int(position_size))
    # def calculate_position_size(self):
    #     entry_price = self.price_comparison.contract_ticker.lastPrice
    #     stop_loss_price = self.calculate_stoploss()
    #     risk_amount = self.calculate_risk_amount()  # B * R
    #     risk_per_unit = abs(entry_price - stop_loss_price)  # P_e * S
    #     contract_value = entry_price * self.quanto_multiplier  # P_e * Q

    #     if risk_per_unit == 0 or contract_value == 0:
    #         raise ValueError("Invalid stop loss or quanto multiplier")

    #     # Position size: C = (B * R) / (Q * P_e * S)
    #     position_size_by_risk = risk_amount / (self.quanto_multiplier * risk_per_unit)
    #     max_contracts_by_balance = (self.balance * self.leverage_max) / contract_value
    #     position_size = min(position_size_by_risk, max_contracts_by_balance)
    #     return max(1, int(position_size))

    def calculate_margin(self):
        contracts = self.calculate_position_size()
        entry_price = self.price_comparison.contract_ticker.lastPrice
        notional_value = contracts * entry_price * self.quanto_multiplier
        return notional_value / self.leverage_max

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.dex_pair import <PERSON><PERSON><PERSON>, DexContract
from models.position_logger import <PERSON><PERSON><PERSON><PERSON>ogger
from collections import defaultdict
from sqlalchemy.dialects.postgresql import insert
from utils.database import reset_dex_pairs_table
from exchanges.dexscreener.models import PairDetails
from exchanges.base import ClientType

MAX_PARAMS = 32767
COLUMNS_PER_ROW = 10
MAX_ROWS_PER_BATCH = MAX_PARAMS // COLUMNS_PER_ROW

BATCH_SIZE = min(1000, MAX_ROWS_PER_BATCH)


class DexService:
    """Service for station-related database operations."""

    @staticmethod
    async def fetch_all_pairs(session: AsyncSession):
        result = await session.execute(select(DexPair))
        pairs = result.scalars().all()
        return pairs

    @staticmethod
    async def get_pairs_grouped_by_chain_id(session: AsyncSession) -> dict[str, list[str]]:
        result = await session.execute(select(DexPair.chainId, DexPair.baseToken_address))
        rows = result.all()

        grouped = defaultdict(set)
        for chain_id, base_address in rows:
            grouped[chain_id].add(base_address)

        return {chain_id: list(addresses) for chain_id, addresses in grouped.items()}

    @staticmethod
    async def create_pair(session: AsyncSession, data) -> DexPair:
        # Check if pair already exists
        stmt = select(DexPair).where(DexPair.pairAddress == data["pairAddress"])
        result = await session.execute(stmt)
        existing = result.scalar_one_or_none()
        if existing:
            raise ValueError("Pair with this address already exists")

        new_pair = DexPair(
            chainId=data["chainId"],
            dexId=data["dexId"],
            url=data["url"],
            pairAddress=data["pairAddress"],
            baseToken_address=data["baseToken"]["address"],
            baseToken_name=data["baseToken"]["name"],
            baseToken_symbol=data["baseToken"]["symbol"],
            quoteToken_address=data["quoteToken"]["address"],
            quoteToken_name=data["quoteToken"]["name"],
            quoteToken_symbol=data["quoteToken"]["symbol"],
        )

        session.add(new_pair)
        await session.commit()
        await session.refresh(new_pair)
        
        return new_pair

    @staticmethod
    async def bulk_create_pairs(session: AsyncSession, data_list: list[PairDetails]):
        if not data_list:
            return

        await reset_dex_pairs_table()

        to_insert = []
        for data in data_list:
            to_insert.append({
                "chainId": data.chainId,
                "dexId": data.chainId,
                "url": data.url,
                "pairAddress": data.pairAddress,
                "baseToken_address": data.baseToken.address,
                "baseToken_name": data.baseToken.name,
                "baseToken_symbol": data.baseToken.symbol,
                "quoteToken_address": data.quoteToken.address,
                "quoteToken_name": data.quoteToken.name,
                "quoteToken_symbol": data.quoteToken.symbol,
            })

        for i in range(0, len(to_insert), BATCH_SIZE):
            batch = to_insert[i:i + BATCH_SIZE]
            stmt = insert(DexPair).values(batch)
            await session.execute(stmt)

        await session.commit()


class DexContractService:
    @staticmethod
    async def fetch_all_contracts(session: AsyncSession, client_type: ClientType):
        result = await session.execute(select(DexContract)
                                       .where(DexContract.client == client_type))
        pairs = result.scalars().all()
        return pairs

    @staticmethod
    async def get_pairs_grouped_by_chain_id(session: AsyncSession, client_type: ClientType) -> dict[str, list[str]]:
        result = await session.execute(
            select(DexContract.chain_id, DexContract.address)
            .where(DexContract.client == client_type)
        )
        rows = result.all()

        grouped = defaultdict(set)
        for chain_id, address in rows:
            grouped[chain_id].add(address)

        return {chain_id: list(addresses) for chain_id, addresses in grouped.items()}

    @staticmethod
    async def create(session: AsyncSession, contract: DexContract):
        session.add(contract)
        await session.commit()
        await session.refresh(contract)

    @staticmethod
    async def bulk_create(session: AsyncSession, contracts: list[DexContract]):
        if not contracts:
            return

        for i in range(0, len(contracts), BATCH_SIZE):
            batch = contracts[i:i + BATCH_SIZE]
            stmt = insert(DexContract).values(
                [c.to_dict(exclude={"id"}) for c in batch]
            ).on_conflict_do_nothing(
                index_elements=["chain_id", "address", "client"]
            )
            await session.execute(stmt)

        await session.commit()

    @staticmethod
    async def bulk_create_or_update(session: AsyncSession, contracts: list[DexContract]):
        if not contracts:
            return

        for i in range(0, len(contracts), BATCH_SIZE):
            batch = contracts[i:i + BATCH_SIZE]
            values = [c.to_dict(exclude={"id"}) for c in batch]

            insert_stmt = insert(DexContract).values(values)
            update_stmt = insert_stmt.on_conflict_do_update(
                index_elements=["chain_id", "address", "client"],
                set_={col: getattr(insert_stmt.excluded, col) for col in values[0].keys()}
            )
            await session.execute(update_stmt)

        await session.commit()


class LoggerService:
    @staticmethod
    async def fetch_all_logs(session: AsyncSession) -> list[PositionLogger]:
        result = await session.execute(select(PositionLogger))
        logs = result.scalars().all()
        return list(logs)

    @staticmethod
    async def get_logs_by_client_type(session: AsyncSession, client_type: ClientType):
        result = await session.execute(
            select(PositionLogger)
            .where(PositionLogger.client_type == client_type)
        )
        logs = result.all()
        return logs

    @staticmethod
    async def create(session: AsyncSession, log: PositionLogger):
        session.add(log)
        await session.commit()
        await session.refresh(log)
        print(f"Created log: {log}")


if __name__ == "__main__":
    import asyncio
    from utils.database import get_session
    async def test_logs():
        session_gen = get_session()
        session: AsyncSession = await anext(session_gen)
        logs = await LoggerService.fetch_all_logs(session)
        print(*logs, sep="\n")
    asyncio.run(test_logs())
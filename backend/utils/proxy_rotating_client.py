import asyncio
import httpx
import time
import random
from typing import Callable, Any
from .proxies import ProxyFetcher


class ProxyRotatingClient:
    """
    A wrapper around httpx.AsyncClient that automatically rotates proxies
    when encountering rate limits (429 status codes).
    """
    
    def __init__(self, rotation_cooldown: int = 10, rotation_delay: float = 2.0):
        """
        Initialize the proxy rotating client.
        
        Args:
            rotation_cooldown: Minimum seconds between proxy rotations
            rotation_delay: Seconds to wait after rotating proxy before resuming
        """
        self.proxies = ProxyFetcher.fetch_proxies()
        self.current_client = None
        self.rotation_lock = asyncio.Lock()
        self.is_rotating = False
        self.rotation_cooldown = rotation_cooldown
        self.rotation_delay = rotation_delay
        self.last_rotation = 0
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        
    async def initialize(self):
        """Initialize the client with a random proxy"""
        if not self.current_client:
            proxy = self._get_random_proxy()
            self.current_client = httpx.AsyncClient(proxy=proxy)
            
    async def close(self):
        """Close the current client"""
        if self.current_client:
            await self.current_client.aclose()
            self.current_client = None
            
    def _get_random_proxy(self) -> str:
        """Get a random proxy from the available list"""
        if not self.proxies:
            self.proxies = ProxyFetcher.fetch_proxies()
        proxy = random.choice(self.proxies)
        self.proxies.remove(proxy)
        return proxy.value
        
    async def _rotate_proxy(self):
        """Rotate proxy and create new client"""
        async with self.rotation_lock:
            if self.is_rotating:
                return
                
            self.is_rotating = True
            print(f"⚠️ Rate limit hit for batch request — rotating proxy and retrying...")
            
            # Close current client
            if self.current_client:
                await self.current_client.aclose()
                
            # Create new client with new proxy
            new_proxy = self._get_random_proxy()
            self.current_client = httpx.AsyncClient(proxy=new_proxy)
            
            # Wait before resuming
            await asyncio.sleep(self.rotation_delay)
            self.is_rotating = False
            
    async def _wait_for_rotation(self):
        """Wait if rotation is in progress"""
        while self.is_rotating:
            await asyncio.sleep(0.1)
            
    async def request(self, method: str, url: str, processor=None, retry_callback: Callable = None, **kwargs) -> httpx.Response:
        """
        Make an HTTP request with automatic proxy rotation on 429 errors.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            processor: AsyncQueueProcessor instance for rate limiting
            retry_callback: Callback function to retry the original operation
            **kwargs: Additional arguments for httpx request
            
        Returns:
            httpx.Response object
            
        Raises:
            Exception: If request fails for reasons other than rate limiting
        """
        await self._wait_for_rotation()
        
        try:
            response = await self.current_client.request(method, url, **kwargs)
            
            if response.status_code == 429:
                # Handle rate limiting
                if processor and hasattr(processor, 'rate_limit_lock') and hasattr(processor, 'last_rotation'):
                    async with processor.rate_limit_lock:
                        now = int(time.time())
                        if now - processor.last_rotation > self.rotation_cooldown:
                            processor.last_rotation = now
                            self.last_rotation = now
                            await self._rotate_proxy()
                            
                            # Retry the original operation if callback provided
                            if retry_callback:
                                await processor.add_task(retry_callback)
                else:
                    # Fallback rotation without processor
                    now = int(time.time())
                    if now - self.last_rotation > self.rotation_cooldown:
                        self.last_rotation = now
                        await self._rotate_proxy()
                        
                return response
                
            return response
            
        except Exception as e:
            raise e
            
    async def get(self, url: str, processor=None, retry_callback: Callable = None, **kwargs) -> httpx.Response:
        """Convenience method for GET requests"""
        return await self.request("GET", url, processor=processor, retry_callback=retry_callback, **kwargs)
        
    async def post(self, url: str, processor=None, retry_callback: Callable = None, **kwargs) -> httpx.Response:
        """Convenience method for POST requests"""
        return await self.request("POST", url, processor=processor, retry_callback=retry_callback, **kwargs)


class AsyncQueueProcessor:
    """
    Asynchronous queue processor for managing concurrent tasks with rate limiting support.
    Used in conjunction with ProxyRotatingClient for handling batch operations.
    """

    def __init__(self, max_concurrent_tasks: int = 10):
        """
        Initialize the async queue processor.

        Args:
            max_concurrent_tasks: Maximum number of concurrent tasks to run
        """
        self.queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.rate_limit_lock = asyncio.Lock()
        self.last_rotation = 0

    async def add_task(self, coro):
        """Add a coroutine to the processing queue"""
        await self.queue.put(coro)

    async def run_worker(self):
        """Worker coroutine that processes tasks from the queue"""
        while True:
            task = await self.queue.get()
            async with self.semaphore:
                await task()
            self.queue.task_done()

    async def run(self, num_workers: int = 10):
        """
        Run the queue processor with specified number of workers.

        Args:
            num_workers: Number of worker coroutines to spawn
        """
        workers = [asyncio.create_task(self.run_worker()) for _ in range(num_workers)]
        await self.queue.join()
        for w in workers:
            w.cancel()

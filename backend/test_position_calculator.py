#!/usr/bin/env python3
"""
Test script for the fixed position calculator
"""

from utils.position_calculator import PositionCalculator
from exchanges.mexc.models import ContractTickerData
from models.pair import PriceComparison

def test_position_calculator():
    """Test the position calculator with realistic values"""
    
    # Create test data similar to the error scenario
    ticker = ContractTickerData(
        symbol="NAORIS_USDT",
        lastPrice=0.0523  # Example price
    )
    
    price_comparison = PriceComparison(
        pair_details=None,
        contract_ticker=ticker
    )
    
    # Test with the balance from the error message
    balance = 126.607732423961  # Available balance from error
    quanto_multiplier = 1.0  # Typical value
    leverage_max = 20  # Typical leverage
    contract_min_qty = 1.0  # Minimum contract size
    contract_max_qty = 1000000.0  # Maximum contract size
    maintenance_rate = 0.005  # 0.5% maintenance rate
    
    calculator = PositionCalculator(
        price_comparison=price_comparison,
        balance=balance,
        quanto_multiplier=quanto_multiplier,
        leverage_max=leverage_max,
        contract_min_qty=contract_min_qty,
        contract_max_qty=contract_max_qty,
        maintenance_rate=maintenance_rate
    )
    
    print("=== Position Calculator Test ===")
    print(f"Balance: {balance}")
    print(f"Entry Price: {ticker.lastPrice}")
    print(f"Quanto Multiplier: {quanto_multiplier}")
    print(f"Max Leverage: {leverage_max}")
    print()
    
    try:
        # Test long position
        position_size = calculator.calculate_position_size(is_long=True)
        margin_required = calculator.calculate_margin(is_long=True)
        
        print(f"Calculated Position Size: {position_size}")
        print(f"Margin Required: {margin_required:.6f}")
        print(f"Available Balance: {balance * 0.95:.6f}")  # With safety margin
        print(f"Margin vs Available: {margin_required / (balance * 0.95) * 100:.2f}%")
        
        if margin_required <= balance * 0.95:
            print("✅ Position size calculation PASSED - within available balance")
        else:
            print("❌ Position size calculation FAILED - exceeds available balance")
            
    except Exception as e:
        print(f"❌ Error in position calculation: {e}")

if __name__ == "__main__":
    test_position_calculator()

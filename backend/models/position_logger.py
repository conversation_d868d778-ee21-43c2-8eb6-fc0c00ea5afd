from sqlalchemy import String, Integer, DateTime
from datetime import datetime, UTC
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from enum import StrEnum
from sqlalchemy.types import Enum as SQLEnum, JSON as S<PERSON>JSO<PERSON>
from exchanges.base import ClientType
from .pair import PositionType
from .dex_pair import Base


class OperationType(StrEnum):
    OPEN = "OPEN"
    CLOSE = "CLOSE"


class PositionLogger(Base):
    __tablename__ = "position_logger"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    created: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=lambda: datetime.now(UTC)
    )

    client_type: Mapped[ClientType] = mapped_column(
        SQLEnum(ClientType, name="client_type_enum"), nullable=False
    )
    session_id: Mapped[str] = mapped_column(String, nullable=False)

    operation_type: Mapped[OperationType] = mapped_column(
        SQLEnum(OperationType, name="operation_type_enum"), nullable=False
    )
    position_type: Mapped[PositionType] = mapped_column(
        SQLEnum(PositionType, name="position_type_enum"), nullable=False
    )
    pair_details_json: Mapped[dict] = mapped_column(SQLJSON, nullable=False)
    contract_ticker_data_json: Mapped[dict] = mapped_column(SQLJSON, nullable=False)

    def __str__(self):
        return f"""
        {{
            created: {self.created},
            client_type: {self.client_type},
            session_id: {self.session_id},
            operation_type: {self.operation_type},
            position_type: {self.position_type},
            pair_details_json: {self.pair_details_json},
            contract_ticker_data_json: {self.contract_ticker_data_json}
        }}
        """

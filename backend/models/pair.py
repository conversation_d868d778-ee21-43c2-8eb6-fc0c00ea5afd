from pydantic import BaseModel
from exchanges.mexc.models import ContractTickerData
from exchanges.dexscreener.models import PairDetails
from typing import Self
from config import (
    MIN_CEX_VOLUME_USD,
    MAX_PRICE_DIFFERENCE_PCT,
    MIN_PRICE_DIFFERENCE_PCT,
)
from enum import Enum

class PositionType(Enum):
    LONG = 1
    SHORT = 2

    @property
    def direction(self) -> str:
        if self == PositionType.LONG:
            return "buy"
        return "sell"

class PriceComparison(BaseModel):
    """Model for price comparison between exchanges"""
    pair_details: PairDetails | None = None
    contract_ticker: ContractTickerData

    def compare(self) -> Self | None:
        try:
            mexc_data = self.contract_ticker
            dex_data = self.pair_details

            # Use direct property access for new model attributes
            mexc_price = float(mexc_data.lastPrice or 0)
            dex_price = float(dex_data.priceUsd or 0)

            if not mexc_price or not dex_price:
                return None

            diff_pct = ((dex_price - mexc_price) / mexc_price) * 100
            if not (MIN_PRICE_DIFFERENCE_PCT < abs(diff_pct) < MAX_PRICE_DIFFERENCE_PCT):
                return None

            mexc_vol = float(mexc_data.volume24 or 0)
            if mexc_vol < MIN_CEX_VOLUME_USD:
                return None

            return self
        except Exception as e:
            print(f"Exception in compare for {getattr(self.contract_ticker, 'symbol', 'N/A')}: {e}")
            return None

    @property
    def diff_percent(self) -> float:
        mexc_price = float(self.contract_ticker.lastPrice or 0)
        dex_price = float(self.pair_details.priceUsd or 0)
        diff_percent = ((dex_price - mexc_price) / mexc_price) * 100 if mexc_price != 0 else 0
        return diff_percent
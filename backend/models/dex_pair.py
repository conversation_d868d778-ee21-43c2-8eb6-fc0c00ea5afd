from sqlmodel import SQLModel, <PERSON>
from sqlalchemy import String, UniqueConstraint, Integer
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class DexPair(SQLModel, table=True):
    __tablename__ = "dex_pairs"
    id: int = Field(default=None, primary_key=True)

    chainId: str
    dexId: str
    url: str
    pairAddress: str

    baseToken_address: str
    baseToken_name: str
    baseToken_symbol: str

    quoteToken_address: str
    quoteToken_name: str
    quoteToken_symbol: str


class Base(DeclarativeBase):
    def to_dict(self, exclude: set[str] = None):
        exclude = exclude or set()
        return {
            col.name: getattr(self, col.name)
            for col in self.__table__.columns
            if col.name not in exclude
        }

class DexContract(Base):
    __tablename__ = "dex_contract"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    chain_id: Mapped[str] = mapped_column(String, nullable=False)
    address: Mapped[str] = mapped_column(String, nullable=False)
    symbol: Mapped[str] = mapped_column(String, nullable=False)
    base_token: Mapped[str] = mapped_column(String)
    quote_token: Mapped[str] = mapped_column(String)
    client: Mapped[str] = mapped_column(String)

    __table_args__ = (
        UniqueConstraint("chain_id", "address", "client", name="uq_chain_symbol"),
    )
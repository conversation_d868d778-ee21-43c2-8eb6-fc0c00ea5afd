# Project commands

`git submodule update --init --force --remote` // force download git submodules on first install

`docker cp /some/file_path.txt <container_name>:/some/file_path.txt` // copy/update files in container while running

# Basic docker commands
`docker compose -f compose/develop.yml up --build -d` // run&build docker compose in background 

`docker ps` // list all containers

`docker stop $(docker ps -a -q)` // stop all containers

`docker exec -it container_name bash` // enter container

`tail -f /dev/null` // keep container active(used for development)

`docker system prune -a` // remove all images, containers and networks

`docker volume prune -a` // remove all volumes

`docker logs --follow container_name` // see logs on a running container

#  Development

`export PYTHONPATH=/app` // fix fastapi dev main.py imports(as ENV var)

# How to install an SSL certificate on a NGINX server

https://www.ssls.com/knowledgebase/how-to-install-an-ssl-certificate-on-a-nginx-server/

# Run celery worker manually, used for development

`celery -A api.celery_app worker -l info`

# Restart commands

`docker restart <container_name>` // restart one container(use it to restart backend container)

`docker exec <nginx_container_name> nginx -s reload` // reload nginx

# How to use let's Let's Encrypt
Let's Encrypt certificates are valid for 90 days. 
Certbot includes a cron job or systemd timer to renew them automatically.

### Install
`apt update`
`apt install certbot python3-certbot-nginx`

### Obtain a Certificate
`certbot --nginx`

### For standalone servers or non-web applications:
`certbot certonly --standalone`

### Verify Installation
`certbot certificates`

### Manual Renewal (if needed)
`certbot renew`

# ngrok for staging

### Start in background
`nohup ngrok http --url=gorgeous-cardinal-arriving.ngrok-free.app 80 > /dev/null 2>&1 &`

### Kill process
`ps aux | grep ngrok` & `kill process_id`
or
`pkill -f ngrok`


POST https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyBHyvfcSQe9acRcl6hcbqW49qtDEbbcRU4
{
  "email": "<EMAIL>",
  "password": "somepassword",
  "returnSecureToken": true
}
FROM debian:bullseye

RUN apt-get update && apt-get install -y  \
    build-essential \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncursesw5-dev \
    libgdbm-dev \
    liblzma-dev \
    zlib1g-dev \
    tk-dev \
    uuid-dev \
    curl \
    ca-certificates \
    checkinstall \
    supervisor \
    wget \
    tor

WORKDIR /usr/local/src
RUN cd /usr/local/src && \
    wget https://www.openssl.org/source/openssl-3.5.1.tar.gz && \
    tar -xvzf openssl-3.5.1.tar.gz && \
    cd openssl-3.5.1 && \
    ./Configure --prefix=/opt/openssl --openssldir=/opt/openssl shared zlib && \
    make -j$(nproc) && \
    make install
ENV PATH="/opt/openssl/bin:$PATH"
ENV LD_LIBRARY_PATH="/opt/openssl/lib:$LD_LIBRARY_PATH"

RUN cd /usr/local/src && \
    wget https://www.python.org/ftp/python/3.13.0/Python-3.13.0.tgz && \
    tar -xf Python-3.13.0.tgz && \
    cd Python-3.13.0 && \
    ./configure \
      --prefix=/opt/python3.13 \
      --with-openssl=/opt/openssl \
      --enable-optimizations \
      --with-ensurepip=install && \
    make -j$(nproc) && \
    make install
ENV PATH="/opt/python3.13/bin:$PATH"
RUN ln -s /opt/python3.13/bin/python3 /usr/bin/python
RUN ln -s /opt/python3.13/bin/pip3 /usr/bin/pip

COPY ./backend/requirements.txt /requirements.txt
RUN python3 -m pip install --upgrade pip
RUN pip3 install -r /requirements.txt

RUN echo "ControlPort 9051\nCookieAuthentication 0" >> /etc/tor/torrc

RUN mkdir /app
COPY ./backend /app
WORKDIR /app
